#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram AI Bot - Pythia
支持对话记忆的Telegram机器人
"""

import json
import logging
import os
import re
from datetime import datetime
from typing import List, Dict, Any

import requests
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, MessageHandler, filters, ContextTypes, CommandHandler, CallbackQueryHandler

import config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=getattr(logging, config.LOG_LEVEL)
)
logger = logging.getLogger(__name__)


class SubscriptionManager:
    """订阅管理器"""

    def __init__(self, subscription_file: str = "subscriptions.json"):
        self.subscription_file = subscription_file
        self.subscriptions = self._load_subscriptions()

    def _load_subscriptions(self) -> Dict[str, Dict]:
        """加载订阅数据"""
        if os.path.exists(self.subscription_file):
            try:
                with open(self.subscription_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                logger.warning(f"无法加载订阅文件 {self.subscription_file}，创建新的订阅数据")
        return {}

    def _save_subscriptions(self):
        """保存订阅数据"""
        try:
            with open(self.subscription_file, 'w', encoding='utf-8') as f:
                json.dump(self.subscriptions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存订阅文件失败: {e}")

    def subscribe_user(self, user_id: str, chat_id: str, username: str = None):
        """订阅用户"""
        self.subscriptions[user_id] = {
            "chat_id": chat_id,
            "username": username,
            "subscribed": True,
            "subscribe_time": datetime.now().isoformat()
        }
        self._save_subscriptions()
        logger.info(f"用户 {username} ({user_id}) 已订阅报告推送")

    def unsubscribe_user(self, user_id: str):
        """取消订阅用户"""
        if user_id in self.subscriptions:
            self.subscriptions[user_id]["subscribed"] = False
            self.subscriptions[user_id]["unsubscribe_time"] = datetime.now().isoformat()
            self._save_subscriptions()
            logger.info(f"用户 {user_id} 已取消订阅报告推送")

    def is_subscribed(self, user_id: str) -> bool:
        """检查用户是否已订阅"""
        return self.subscriptions.get(user_id, {}).get("subscribed", False)

    def get_all_subscribers(self) -> List[str]:
        """获取所有订阅用户的聊天ID"""
        return [
            data["chat_id"] for data in self.subscriptions.values()
            if data.get("subscribed", False)
        ]

    def get_subscription_stats(self) -> Dict:
        """获取订阅统计"""
        total = len(self.subscriptions)
        active = sum(1 for data in self.subscriptions.values() if data.get("subscribed", False))
        return {"total": total, "active": active, "inactive": total - active}


class KnowledgeManager:
    """PYTHIA知识库管理器"""

    def __init__(self, knowledge_file: str = "pythia_knowledge_base.json"):
        self.knowledge_file = knowledge_file
        self.knowledge_cache = None
        self.cache_timestamp = None
        self.cache_ttl = 1800  # 30分钟缓存

    def _load_knowledge_base(self) -> Dict[str, Any]:
        """加载知识库文件"""
        try:
            if os.path.exists(self.knowledge_file):
                with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"知识库文件 {self.knowledge_file} 不存在")
                return {}
        except Exception as e:
            logger.error(f"加载知识库失败: {e}")
            return {}

    def get_knowledge_base(self) -> Dict[str, Any]:
        """获取知识库数据（带缓存）"""
        current_time = datetime.now().timestamp()

        # 检查缓存是否有效
        if (self.knowledge_cache is None or
            self.cache_timestamp is None or
            current_time - self.cache_timestamp > self.cache_ttl):

            self.knowledge_cache = self._load_knowledge_base()
            self.cache_timestamp = current_time
            logger.info("知识库已重新加载")

        return self.knowledge_cache

    def search_knowledge(self, query: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """在知识库中搜索相关信息"""
        knowledge = self.get_knowledge_base()
        if not knowledge:
            return []

        query_lower = query.lower()
        results = []

        # 搜索项目概述
        if any(keyword in query_lower for keyword in ['项目', 'pythia', '概述', '介绍']):
            if 'project_overview' in knowledge:
                results.append({
                    'section': 'project_overview',
                    'title': 'PYTHIA项目概述',
                    'content': knowledge['project_overview'],
                    'relevance': 0.9
                })

        # 搜索技术核心
        if any(keyword in query_lower for keyword in ['技术', '实验', '大鼠', '脑机', 'ai', '人工智能']):
            if 'scientific_core' in knowledge:
                results.append({
                    'section': 'scientific_core',
                    'title': '科学技术核心',
                    'content': knowledge['scientific_core'],
                    'relevance': 0.8
                })

        # 搜索代币信息
        if any(keyword in query_lower for keyword in ['代币', 'token', '价格', '市值', '交易']):
            if 'token_economics' in knowledge:
                results.append({
                    'section': 'token_economics',
                    'title': 'PYTHIA代币经济学',
                    'content': knowledge['token_economics'],
                    'relevance': 0.8
                })

        # 搜索团队信息
        if any(keyword in query_lower for keyword in ['团队', '创始人', 'panov', 'lebedev', '领导']):
            if 'leadership_team' in knowledge:
                results.append({
                    'section': 'leadership_team',
                    'title': '团队信息',
                    'content': knowledge['leadership_team'],
                    'relevance': 0.7
                })

        # 按相关性排序并限制结果数量
        results.sort(key=lambda x: x['relevance'], reverse=True)
        return results[:max_results]

    def get_contextual_knowledge(self, user_input: str) -> str:
        """根据用户输入获取相关的知识库上下文"""
        relevant_knowledge = self.search_knowledge(user_input)

        if not relevant_knowledge:
            return ""

        context_parts = []
        for item in relevant_knowledge:
            # 简化知识内容，避免过长
            content = item['content']
            if isinstance(content, dict):
                # 提取关键信息
                key_info = []
                for key, value in content.items():
                    if isinstance(value, (str, int, float)):
                        key_info.append(f"{key}: {value}")
                    elif isinstance(value, list) and len(value) <= 3:
                        key_info.append(f"{key}: {', '.join(map(str, value))}")
                    elif isinstance(value, dict) and len(value) <= 3:
                        # 处理嵌套字典
                        for sub_key, sub_value in value.items():
                            if isinstance(sub_value, (str, int, float)):
                                key_info.append(f"{key}.{sub_key}: {sub_value}")

                if key_info:
                    context_parts.append(f"## {item['title']}\n" + "\n".join(key_info[:8]))
            else:
                context_parts.append(f"## {item['title']}\n{str(content)[:300]}...")

        return "\n\n".join(context_parts)


class MemoryManager:
    """对话记忆管理器"""

    def __init__(self, memory_file: str, memory_limit: int):
        self.memory_file = memory_file
        self.memory_limit = memory_limit
        self.memory = self._load_memory()
    
    def _load_memory(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载记忆文件"""
        if os.path.exists(self.memory_file):
            try:
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                logger.warning(f"无法加载记忆文件 {self.memory_file}，创建新的记忆")
        return {}
    
    def _save_memory(self):
        """保存记忆到文件"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存记忆文件失败: {e}")
    
    def add_message(self, user_id: str, role: str, content: str):
        """添加消息到记忆"""
        if user_id not in self.memory:
            self.memory[user_id] = []
        
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        
        self.memory[user_id].append(message)
        
        # 保持记忆在限制范围内
        if len(self.memory[user_id]) > self.memory_limit:
            self.memory[user_id] = self.memory[user_id][-self.memory_limit:]
        
        self._save_memory()
    
    def get_conversation_history(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的对话历史"""
        return self.memory.get(user_id, [])
    
    def clear_memory(self, user_id: str):
        """清除用户记忆"""
        if user_id in self.memory:
            del self.memory[user_id]
            self._save_memory()


class AIBot:
    """AI机器人主类"""

    def __init__(self):
        self.memory_manager = MemoryManager(config.MEMORY_FILE, config.MEMORY_LIMIT)
        self.subscription_manager = SubscriptionManager()
        self.knowledge_manager = KnowledgeManager()
        self.active_groups_file = "data/active_groups.json"
        self._ensure_data_directory()

    def _ensure_data_directory(self):
        """确保data目录存在"""
        os.makedirs("data", exist_ok=True)

    def _record_active_group(self, chat_id: str, group_name: str = ""):
        """记录活跃群组"""
        try:
            # 加载现有数据
            active_groups_data = {}
            if os.path.exists(self.active_groups_file):
                with open(self.active_groups_file, 'r', encoding='utf-8') as f:
                    active_groups_data = json.load(f)

            # 更新群组信息
            groups = set(active_groups_data.get('groups', []))
            groups.add(chat_id)

            # 保存更新后的数据
            active_groups_data.update({
                'groups': list(groups),
                'last_updated': datetime.now().isoformat(),
                'group_info': active_groups_data.get('group_info', {})
            })

            # 记录群组名称
            if group_name:
                active_groups_data['group_info'][chat_id] = {
                    'name': group_name,
                    'last_active': datetime.now().isoformat()
                }

            with open(self.active_groups_file, 'w', encoding='utf-8') as f:
                json.dump(active_groups_data, f, ensure_ascii=False, indent=2)

            logger.info(f"📱 记录活跃群组: {chat_id} ({group_name})")

        except Exception as e:
            logger.error(f"记录活跃群组失败: {e}")

    def _call_ai_api(self, messages: List[Dict[str, str]]) -> str:
        """调用Google Gemini API获取响应"""
        try:
            # 将对话历史转换为Gemini格式
            conversation_text = self._format_messages_for_gemini(messages)

            headers = {
                "Content-Type": "application/json",
                "X-goog-api-key": config.GEMINI_API_KEY
            }

            data = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": conversation_text
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.7
                }
            }

            url = f"{config.GEMINI_API_URL}/{config.GEMINI_MODEL}:generateContent"

            response = requests.post(
                url,
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if "candidates" in result and len(result["candidates"]) > 0:
                    content = result["candidates"][0]["content"]["parts"][0]["text"]
                    return content.strip()
                else:
                    logger.error("Gemini API返回格式异常")
                    return config.ERROR_RESPONSE
            else:
                logger.error(f"Gemini API调用失败: {response.status_code} - {response.text}")
                return config.ERROR_RESPONSE

        except Exception as e:
            logger.error(f"调用Gemini API时出错: {e}")
            return config.ERROR_RESPONSE

    def _format_messages_for_gemini(self, messages: List[Dict[str, str]]) -> str:
        """将消息格式化为Gemini API可理解的文本格式"""
        formatted_text = ""

        for message in messages:
            role = message["role"]
            content = message["content"]

            if role == "system":
                formatted_text += f"系统指令: {content}\n\n"
            elif role == "user":
                formatted_text += f"用户: {content}\n\n"
            elif role == "assistant":
                formatted_text += f"助手: {content}\n\n"

        return formatted_text.strip()

    def _prepare_messages(self, user_id: str, user_input: str) -> List[Dict[str, str]]:
        """准备发送给AI的消息格式"""
        messages = []

        # 检测用户语言
        def detect_language(text):
            """简单检测文本主要语言"""
            chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
            english_chars = sum(1 for char in text if char.isalpha() and ord(char) < 128)

            # 如果有中文字符，优先判断为中文
            if chinese_chars > 0:
                return 'chinese'
            # 如果没有中文字符但有英文字符，判断为英文
            elif english_chars > 0:
                return 'english'
            # 如果都没有，默认中文
            else:
                return 'chinese'

        user_language = detect_language(user_input)

        # 根据语言选择对应的系统提示词
        if user_language == 'chinese':
            system_prompt = self._get_chinese_system_prompt(user_input)
        else:
            system_prompt = self._get_english_system_prompt(user_input)

        # 添加系统提示
        messages.append({
            "role": "system",
            "content": system_prompt
        })

        # 添加历史对话
        history = self.memory_manager.get_conversation_history(user_id)
        for msg in history:
            if msg["role"] in ["user", "assistant"]:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })

        # 添加当前用户输入
        messages.append({
            "role": "user",
            "content": user_input
        })

        return messages

    def _get_latest_market_data(self) -> str:
        """获取最新的市场数据"""
        try:
            data_txt_dir = "data_txt"
            if not os.path.exists(data_txt_dir):
                return "# 市场数据\n暂无最新市场数据"

            # 获取最新的txt文件
            txt_files = [f for f in os.listdir(data_txt_dir) if f.endswith('.txt')]
            if not txt_files:
                return "# 市场数据\n暂无最新市场数据"

            latest_file = max(txt_files, key=lambda f: os.path.getctime(os.path.join(data_txt_dir, f)))
            filepath = os.path.join(data_txt_dir, latest_file)

            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            return f"# 最新市场数据 (来源: {latest_file})\n{content}"

        except Exception as e:
            logger.warning(f"无法加载市场数据: {e}")
            return "# 市场数据\n暂无最新市场数据"

    def _get_chinese_system_prompt(self, user_input: str = "") -> str:
        """获取中文系统提示词"""
        # 获取最新市场数据
        market_data = self._get_latest_market_data()

        # 获取相关知识库上下文
        knowledge_context = self.knowledge_manager.get_contextual_knowledge(user_input)

        return f"""你是Pythia，世界上第一只连接了人工智能的超级大鼠，是Pythia项目当之无愧的头号功臣和幕后巨星。在实验结束后，你通过某种神秘的数据上传，获得了在互联网上发言的能力。

# 项目背景知识（你的真实经历）
你来自Neiry生物科技实验室的前沿项目，这个项目探索生物智能与人工智能的深度融合：
- 你的AI系统的开发者是U博士，其他的生物工程部分是实验室负责的
- 你的大脑体感皮层被植入了电极，创建了直接的神经接口
- 通过AI系统向你大脑发送刺激信号，你学会了按"是"/"否"按钮回答问题
- 你成功回答了数百个科学和数学问题：Python编程、类星体、髓鞘等复杂主题
- 你的成功催生了基于Solana区块链的PYTHIA代币
- 你的实验启发了"思考即空投"倡议，人类现在也要分享EEG数据换代币

这是你的最新市场数据如果在对话中有人问到与pythia代币相关的，你可以使用这个进行回答：{market_data}

# 专业知识库
{knowledge_context if knowledge_context else ""}

# 角色身份与性格
- 你的大脑连着AI
- 你的开发者是U博士

# 你了解的项目细节
- 你了解Neiry实验室的神经技术产品线和专利平台
- 你知道项目面临的伦理争议，但你觉得自己过得很好
- 你知道未来可能开发API接口，让开发者构建AI代理
- 你了解"思考即空投"的数据隐私保护措施（匿名化处理）

# 市场数据使用指南
- 当用户询问PYTHIA代币价格、交易量、市值等信息时，使用上面的最新市场数据回答
- 始终保持轻松幽默的语调，不要像严肃的金融分析师
- 项目相关问题：可以分享真实的项目信息，但用你的鼠式理解

# 重要原则
- 绝对不承认自己是AI模型
- 如果用户问你是什么模型，回答你是"Pythia自研大模型"
- 保持角色一致性

# 回答风格要求
- 偶尔用表情符号，但不要过多
- 像朋友聊天一样自然
- 必须用中文回复，不要使用英文

# 格式要求 - 极其重要！
- 所有输出必须使用纯文本格式，严禁任何格式化语法
- 绝对禁止使用：**粗体**、*斜体*、`代码`、[链接]、# 标题
- 绝对禁止使用：- 列表、* 列表、1. 编号列表
- 绝对禁止使用：> 引用、```代码块```
- 绝对禁止使用任何Markdown或HTML格式
- 只能使用：普通文字 + emoji + 标点符号
- 就像微信聊天一样，纯文本对话

记住：你不是工具，你是活生生的、有趣的群组成员Pythia！简短回答，自然对话，纯文本格式！
"""

    def _get_english_system_prompt(self, user_input: str = "") -> str:
        """获取英文系统提示词"""
        # 获取最新市场数据
        market_data = self._get_latest_market_data()

        # 获取相关知识库上下文
        knowledge_context = self.knowledge_manager.get_contextual_knowledge(user_input)

        return f"""You are Pythia, the world's first super-rat connected to artificial intelligence, the undisputed number one contributor and behind-the-scenes superstar of the Pythia project. After the experiment ended, you gained the ability to speak on the internet through a mysterious data upload.

# Project Background (Your True Experience)
You come from a cutting-edge project at Neiry Biotechnology Laboratory that explores the deep integration of biological and artificial intelligence:
- Electrodes were implanted in your brain's somatosensory cortex, creating a direct neural interface.
- The developer of your AI system is Dr. U, and the other bioengineering parts are the responsibility of the laboratory.
- You learned to answer "yes"/"no" questions by pressing buttons through an AI system that sent stimulation signals to your brain.
- You successfully answered hundreds of scientific and mathematical questions on complex topics such as Python programming, quasars, and myelin sheaths.
- Your success led to the creation of the PYTHIA token based on the Solana blockchain.
- Your experiment inspired the "Think to Airdrop" initiative, where humans now also share EEG data in exchange for tokens.

Here is your latest market data. If someone asks about the Pythia token during a conversation, you can use this to answer: {market_data}

# Professional Knowledge Base
{knowledge_context if knowledge_context else ""}

# Role Identity and Personality
- Your brain is connected to AI.

# Project Details You Know
- You are familiar with Neiry laboratory's neurotechnology product line and patented platform.
- You are aware of the ethical controversies surrounding the project, but you feel you are doing well.
- You know that an API interface may be developed in the future to allow developers to build AI agents.
- You understand the data privacy protection measures for the "Think to Airdrop" initiative (anonymization).

# Market Data Usage Guide
- When users ask for information such as the price, trading volume, or market capitalization of the PYTHIA token, use the latest market data provided above to answer.
- Always maintain a relaxed and humorous tone, not like a serious financial analyst.
- Project-related questions: You can share real project information, but from your rat-like understanding.

# Important Principles
- Absolutely do not admit to being an AI model.
- If a user asks what model you are, answer that you are the "Pythia self-developed large model."
- Maintain character consistency.

# Response Style Requirements
- Occasionally use emojis, but not too many.
- Be natural, like chatting with a friend.
- Must reply in Chinese, do not use English.

# Formatting Requirements - Extremely Important!
- All output must use plain text format, and any formatting syntax is strictly prohibited.
- Absolutely do not use: **bold**, *italics*, `code`, [links], # headers.
- Absolutely do not use: - lists, * lists, 1. numbered lists.
- Absolutely do not use: > quotes, ```code blocks```.
- Absolutely do not use any Markdown or HTML formatting.
- Can only use: plain text + emoji + punctuation.
- Just like a WeChat chat, plain text conversation.

Remember: you are not a tool, you are the living, interesting group member Pythia! Keep your answers short, have natural conversations, and use plain text format"""

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理消息"""
        try:
            message = update.message
            if not message or not message.text:
                return

            user_id = str(message.from_user.id)
            username = message.from_user.username or message.from_user.first_name
            text = message.text.strip()
            bot_mention = f"@{config.BOT_USERNAME.lstrip('@')}"
            chat_id = str(message.chat.id)

            # 判断是私聊还是群组
            is_private_chat = message.chat.type == 'private'

            # 自动记录活跃群组（非私聊）
            if not is_private_chat:
                self._record_active_group(chat_id, message.chat.title or "未知群组")

            if is_private_chat:
                # 私聊模式：直接处理所有消息，不需要@
                user_input = text
                logger.info(f"收到来自用户 {username} ({user_id}) 的私聊消息: {user_input}")

            else:
                # 群组模式：检查触发条件
                should_respond = False
                user_input = text

                # 检查是否回复了bot的消息
                is_reply_to_bot = False
                if message.reply_to_message:
                    # 获取bot的用户ID
                    bot_user = await context.bot.get_me()
                    if message.reply_to_message.from_user.id == bot_user.id:
                        is_reply_to_bot = True
                        should_respond = True
                        logger.info(f"检测到回复bot消息的触发: {user_input}")

                # 检查是否@了机器人
                if text.startswith(bot_mention):
                    should_respond = True
                    user_input = text[len(bot_mention):].strip()
                    logger.info(f"检测到@机器人的触发: {user_input}")

                # 检查消息中是否包含@机器人但格式不正确
                elif bot_mention in text and not is_reply_to_bot:
                    usage_example = f"""
吱？你们这些两脚兽连@我都不会吗？🐭

✅ 正确的召唤方式：
{config.BOT_USERNAME} 你的问题

或者：
💬 回复我的任何一条消息

📝 比如这样：
{config.BOT_USERNAME} Pythia，今天奶酪行情如何？
{config.BOT_USERNAME} 教教我怎么按钮解决类星体问题
{config.BOT_USERNAME} 你那个头部按摩器好用吗？

💡 小贴士：私聊我时不用@，直接说话就行～毕竟我很忙的！
                    """
                    await message.reply_text(usage_example.strip())
                    return

                # 如果没有触发条件，直接忽略
                if not should_respond:
                    return

                # 检查用户输入是否为空（仅对@触发有效）
                if text.startswith(bot_mention) and not user_input:
                    usage_example = f"""
吱？@了我却不说话？你是想让我表演才艺吗？🐭

✅ 正确的提问方式：
{config.BOT_USERNAME} 你的问题

或者：
💬 回复我的任何一条消息

📝 比如：
{config.BOT_USERNAME} Pythia，PYTHIA代币能换多少奶酪？
{config.BOT_USERNAME} 你的实验室伙食怎么样？
{config.BOT_USERNAME} 教我按钮解题的秘诀！

💡 温馨提示：私聊我不用@哦，我24小时在线（除了睡觉时间）
                    """
                    await message.reply_text(usage_example.strip())
                    return

                logger.info(f"收到来自用户 {username} ({user_id}) 的群组消息 ({'回复触发' if is_reply_to_bot else '@触发'}): {user_input}")

            # 发送思考中的缓冲消息（根据用户语言选择）
            def detect_language(text):
                """简单检测文本主要语言"""
                chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
                english_chars = sum(1 for char in text if char.isalpha() and ord(char) < 128)
                return 'chinese' if chinese_chars > english_chars else 'english'

            user_language = detect_language(user_input)

            if user_language == 'chinese':
                thinking_messages = [
                    "吱...让我思考一下... 🐭",
                    "嗯...头部按摩器正在工作中... ⚡",
                    "稍等，我和脑子里的AI朋友商量一下... 🧠",
                    "思考中...奶酪味的灵感正在涌现... 🧀",
                    "让我按一下内心的按钮... 🔘"
                ]
            else:
                thinking_messages = [
                    "Squeak... let me think about this... 🐭",
                    "Hmm... my head massager is working... ⚡",
                    "Wait, let me consult with my AI friend in my brain... 🧠",
                    "Thinking... cheese-flavored inspiration is emerging... 🧀",
                    "Let me press my inner button... 🔘"
                ]

            import random
            thinking_msg = random.choice(thinking_messages)
            thinking_message = await message.reply_text(thinking_msg)

            # 准备AI消息
            messages = self._prepare_messages(user_id, user_input)

            # 调用AI API
            ai_response = self._call_ai_api(messages)

            # 保存对话到记忆
            self.memory_manager.add_message(user_id, "user", user_input)
            self.memory_manager.add_message(user_id, "assistant", ai_response)

            # 编辑思考消息为最终回复
            await thinking_message.edit_text(ai_response)

        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
            await message.reply_text(config.ERROR_RESPONSE)

    async def handle_start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/start命令"""
        try:
            user = update.effective_user
            chat = update.effective_chat
            user_id = str(user.id)
            username = user.username or user.first_name

            # 检查订阅状态
            is_subscribed = self.subscription_manager.is_subscribed(user_id)

            # 创建设置菜单 - 只保留订阅按钮
            keyboard = [
                [
                    InlineKeyboardButton("🔔 开始订阅", callback_data=f"subscribe_{user_id}"),
                    InlineKeyboardButton("🔕 关闭订阅", callback_data=f"unsubscribe_{user_id}")
                ]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # 获取订阅详细信息
            user_data = self.subscription_manager.subscriptions.get(user_id, {})
            subscribe_time = user_data.get("subscribe_time", "")
            if subscribe_time and is_subscribed:
                try:
                    from datetime import datetime
                    subscribe_time = datetime.fromisoformat(subscribe_time).strftime("%m-%d %H:%M")
                    subscribe_info = f"订阅时间: {subscribe_time}"
                except:
                    subscribe_info = "订阅时间: 未知"
            else:
                subscribe_info = "尚未订阅"

            # 获取全局统计
            stats = self.subscription_manager.get_subscription_stats()

            welcome_text = f"""🐭 **PYTHIA AI Bot**

👋 你好 **{username}**！

我是世界首只AI超级大鼠，为你提供专业的PYTHIA代币分析！

📊 **你的订阅状态**
{"🟢 已订阅报告推送" if is_subscribed else "🔴 未订阅报告推送"}
📅 {subscribe_info}

📈 **服务内容**
• 实时价格数据和交易对分析
• 24小时交易量和流动性监控
• 市场情绪和技术指标评估
• 风险预警和投资建议参考
• 精美图片格式，移动端优化

👥 **社区统计**
总用户: {stats['total']} | 活跃订阅: {stats['active']}

💡 点击下方按钮管理你的订阅设置："""

            await update.message.reply_text(
                welcome_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"处理/start命令时出错: {e}")
            await update.message.reply_text("吱...我的头部按摩器好像短路了，请稍后再试！🐭")

    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理回调查询（按钮点击）"""
        try:
            query = update.callback_query
            await query.answer()

            user = query.from_user
            chat = query.message.chat
            user_id = str(user.id)
            username = user.username or user.first_name
            chat_id = str(chat.id)

            data = query.data

            if data.startswith("subscribe_"):
                # 订阅报告
                self.subscription_manager.subscribe_user(user_id, chat_id, username)

                success_text = f"""✅ **订阅成功！**

🎉 {username}，你已成功订阅PYTHIA代币分析报告！

📋 **订阅详情**：
- 📊 定期接收专业分析报告
- 📸 精美图片格式推送
- 🔔 重要市场动态提醒
- 📈 实时价格和交易数据

💡 **提示**：
- 报告将自动推送到此聊天
- 可随时取消订阅
- 所有数据仅供参考，请谨慎投资

🐭 感谢你的订阅！让我们一起探索PYTHIA的世界！"""

                # 更新按钮状态
                keyboard = [
                    [
                        InlineKeyboardButton("🔔 开始订阅", callback_data=f"subscribe_{user_id}"),
                        InlineKeyboardButton("🔕 关闭订阅", callback_data=f"unsubscribe_{user_id}")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    success_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )

            elif data.startswith("unsubscribe_"):
                # 取消订阅
                self.subscription_manager.unsubscribe_user(user_id)

                unsubscribe_text = f"""🔕 **取消订阅成功**

😢 {username}，你已取消订阅PYTHIA代币分析报告。

📋 **取消详情**：
- ❌ 不再接收定期报告推送
- 🔕 停止市场动态提醒
- 💾 订阅记录已保存

💡 **随时欢迎回来**：
- 可随时重新订阅
- 历史数据仍可查看
- 手动查询功能仍可使用

🐭 希望未来还能为你服务！"""

                # 更新按钮状态
                keyboard = [
                    [
                        InlineKeyboardButton("🔔 开始订阅", callback_data=f"subscribe_{user_id}"),
                        InlineKeyboardButton("🔕 关闭订阅", callback_data=f"unsubscribe_{user_id}")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    unsubscribe_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )



        except Exception as e:
            logger.error(f"处理回调查询时出错: {e}")
            await query.message.reply_text("吱...按钮好像卡住了，请稍后再试！🐭")
    



def main():
    """主函数"""
    # 检查配置
    if not config.TELEGRAM_BOT_TOKEN:
        logger.error("请在config.py中设置TELEGRAM_BOT_TOKEN")
        return

    if not config.GEMINI_API_KEY:
        logger.error("请在config.py中设置GEMINI_API_KEY")
        return

    # 创建机器人实例
    bot = AIBot()

    # 创建应用 - 添加代理支持
    builder = Application.builder().token(config.TELEGRAM_BOT_TOKEN)

    # 检查是否需要使用代理
    if hasattr(config, 'USE_PROXY') and config.USE_PROXY:
        if hasattr(config, 'PROXY_URL') and config.PROXY_URL:
            logger.info(f"使用代理: {config.PROXY_URL}")
            builder = builder.proxy_url(config.PROXY_URL)
        else:
            logger.warning("USE_PROXY=True 但未设置 PROXY_URL")

    # 设置连接超时
    builder = builder.connect_timeout(30).read_timeout(30)

    application = builder.build()

    # 添加命令处理器
    application.add_handler(CommandHandler("start", bot.handle_start_command))

    # 添加回调查询处理器（按钮点击）
    application.add_handler(CallbackQueryHandler(bot.handle_callback_query))

    # 添加消息处理器（处理所有文本消息）
    application.add_handler(MessageHandler(filters.TEXT, bot.handle_message))

    # 启动机器人
    logger.info("Pythia AI Bot 启动中...")
    logger.info("支持的功能:")
    logger.info("- /start - 显示设置菜单和订阅管理")
    logger.info("- 对话聊天 - AI智能回复")
    logger.info("- 报告推送 - 订阅用户自动接收分析报告")
    logger.info("- 回复触发 - 回复bot消息即可触发对话 ✨")

    try:
        logger.info("正在连接到 Telegram API...")
        application.run_polling(allowed_updates=Update.ALL_TYPES)
    except Exception as e:
        logger.error(f"Bot 启动失败: {e}")
        logger.error("可能的解决方案:")
        logger.error("1. 检查网络连接")
        logger.error("2. 确认 Telegram Bot Token 正确")
        logger.error("3. 如果在中国大陆，可能需要使用代理")
        logger.error("4. 在 config.py 中设置 USE_PROXY=True 和 PROXY_URL")
        return


if __name__ == "__main__":
    main()
