#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示知识库集成功能
展示机器人如何根据不同问题智能检索和使用知识库内容
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bot import KnowledgeManager

def demo_knowledge_retrieval():
    """演示知识检索功能"""
    print("🎭 PYTHIA知识库集成演示")
    print("=" * 50)
    
    # 创建知识库管理器
    km = KnowledgeManager()
    
    # 模拟不同类型的用户问题
    demo_questions = [
        {
            "question": "PYTHIA项目是什么？",
            "category": "项目概述",
            "expected": "应该返回项目基本信息"
        },
        {
            "question": "PYTHIA代币现在多少钱？",
            "category": "代币信息", 
            "expected": "应该返回代币经济学信息"
        },
        {
            "question": "团队有哪些核心成员？",
            "category": "团队信息",
            "expected": "应该返回领导团队信息"
        },
        {
            "question": "大鼠实验是怎么做的？",
            "category": "技术原理",
            "expected": "应该返回科学技术核心信息"
        },
        {
            "question": "这个项目有什么创新点？",
            "category": "项目特色",
            "expected": "应该返回项目概述或技术核心"
        }
    ]
    
    for i, demo in enumerate(demo_questions, 1):
        print(f"\n📝 演示 {i}: {demo['category']}")
        print(f"❓ 用户问题: \"{demo['question']}\"")
        print(f"🎯 预期结果: {demo['expected']}")
        
        # 搜索相关知识
        results = km.search_knowledge(demo['question'])
        
        print(f"🔍 搜索结果:")
        if results:
            for j, result in enumerate(results, 1):
                print(f"  {j}. {result['title']} (相关性: {result['relevance']})")
        else:
            print("  ❌ 未找到相关结果")
        
        # 获取上下文
        context = km.get_contextual_knowledge(demo['question'])
        
        print(f"📋 生成的上下文:")
        if context:
            # 显示上下文的前200个字符
            preview = context.replace('\n', ' ')[:200]
            print(f"  ✅ 上下文长度: {len(context)} 字符")
            print(f"  📄 内容预览: {preview}...")
        else:
            print("  ❌ 未生成上下文")
        
        print("-" * 50)

def demo_system_prompt_integration():
    """演示系统提示词集成"""
    print("\n🤖 系统提示词集成演示")
    print("=" * 50)
    
    # 模拟AIBot的提示词生成过程
    from bot import AIBot
    
    # 创建机器人实例
    bot = AIBot()
    
    # 测试不同问题的提示词生成
    test_inputs = [
        "告诉我PYTHIA项目的详细信息",
        "PYTHIA代币的价格走势如何？",
        "团队背景怎么样？"
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n🧪 测试 {i}: \"{user_input}\"")
        
        # 生成中文系统提示词
        system_prompt = bot._get_chinese_system_prompt(user_input)
        
        # 检查是否包含知识库内容
        if "# 专业知识库" in system_prompt:
            print("✅ 系统提示词已包含知识库内容")
            
            # 提取知识库部分
            lines = system_prompt.split('\n')
            kb_start = -1
            kb_end = -1
            
            for j, line in enumerate(lines):
                if "# 专业知识库" in line:
                    kb_start = j
                elif kb_start != -1 and line.startswith("# ") and "专业知识库" not in line:
                    kb_end = j
                    break
            
            if kb_start != -1:
                if kb_end == -1:
                    kb_end = len(lines)
                
                kb_content = '\n'.join(lines[kb_start:kb_end])
                print(f"📚 知识库内容长度: {len(kb_content)} 字符")
                
                if len(kb_content.strip()) > 20:  # 有实际内容
                    print("📄 知识库内容预览:")
                    preview_lines = kb_content.split('\n')[:5]
                    for line in preview_lines:
                        if line.strip():
                            print(f"    {line[:80]}...")
                else:
                    print("📄 知识库内容为空（该问题未匹配到相关知识）")
        else:
            print("❌ 系统提示词未包含知识库内容")
        
        print("-" * 30)

def demo_performance_metrics():
    """演示性能指标"""
    print("\n⚡ 性能指标演示")
    print("=" * 50)
    
    import time
    
    km = KnowledgeManager()
    
    # 测试缓存性能
    print("🔄 测试缓存性能...")
    
    # 第一次加载（冷启动）
    start_time = time.time()
    knowledge1 = km.get_knowledge_base()
    cold_load_time = time.time() - start_time
    
    # 第二次加载（缓存命中）
    start_time = time.time()
    knowledge2 = km.get_knowledge_base()
    cache_hit_time = time.time() - start_time
    
    print(f"📊 性能对比:")
    print(f"  🥶 冷启动加载: {cold_load_time:.4f} 秒")
    print(f"  🚀 缓存命中: {cache_hit_time:.4f} 秒")
    print(f"  📈 性能提升: {cold_load_time/cache_hit_time:.1f}x")
    
    # 测试搜索性能
    print(f"\n🔍 测试搜索性能...")
    
    test_queries = ["PYTHIA", "代币", "团队", "技术", "实验"] * 10  # 50次搜索
    
    start_time = time.time()
    for query in test_queries:
        km.search_knowledge(query)
    search_time = time.time() - start_time
    
    print(f"📊 搜索性能:")
    print(f"  🔎 总搜索次数: {len(test_queries)}")
    print(f"  ⏱️ 总耗时: {search_time:.4f} 秒")
    print(f"  🚀 平均每次: {search_time/len(test_queries):.4f} 秒")
    
    # 内存使用情况
    print(f"\n💾 内存使用情况:")
    print(f"  📚 知识库大小: {len(str(knowledge1))} 字符")
    print(f"  🗂️ 主要部分数: {len(knowledge1)}")
    print(f"  🏷️ 缓存状态: {'已缓存' if km.knowledge_cache else '未缓存'}")

def main():
    """主演示函数"""
    print("🎉 欢迎使用PYTHIA知识库集成演示！")
    print("本演示将展示知识库如何智能地集成到聊天机器人中\n")
    
    try:
        # 演示1: 知识检索功能
        demo_knowledge_retrieval()
        
        # 演示2: 系统提示词集成
        demo_system_prompt_integration()
        
        # 演示3: 性能指标
        demo_performance_metrics()
        
        print("\n🎊 演示完成！")
        print("\n💡 总结:")
        print("  ✅ 知识库成功集成到机器人系统")
        print("  ✅ 智能检索功能正常工作") 
        print("  ✅ 系统提示词动态增强")
        print("  ✅ 缓存机制提供优异性能")
        print("  ✅ 机器人现在具备专业的PYTHIA项目知识")
        
        print("\n🚀 现在您的机器人已经准备好回答关于PYTHIA项目的专业问题了！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        print("请检查知识库文件是否存在且格式正确")

if __name__ == "__main__":
    main()
