#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PYTHIA代币全面分析与报告发送器 - 完整整合版本
整合了HTML转图片和PYTHIA分析功能的完整解决方案
包含：数据分析、报告生成、HTML转图片、Telegram发送、文件监控等功能
"""

import os
import asyncio
import time
import requests
import json
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import shutil
import config
from config import PYTHIA_TOKEN_INFO, API_CONFIG, ANALYSIS_CONFIG, SEARCH_KEYWORDS, OUTPUT_CONFIG

# 🔧 Telegram发送开关 - 设置为False关闭发送功能
ENABLE_TELEGRAM_SEND = True  # 改为True开启，False关闭

# 导入配置文件
try:
    from config import OUTPUT_CONFIG
    TELEGRAM_BOT_TOKEN = OUTPUT_CONFIG["telegram"]["bot_token"]
    TELEGRAM_CHAT_ID = OUTPUT_CONFIG["telegram"]["chat_id"]
except ImportError:
    print("⚠️ 无法导入config.py配置文件")
    TELEGRAM_BOT_TOKEN = None
    TELEGRAM_CHAT_ID = None
except KeyError as e:
    print(f"⚠️ 配置文件中缺少Telegram配置项: {e}")
    TELEGRAM_BOT_TOKEN = None
    TELEGRAM_CHAT_ID = None

class HeadlessHTMLConverter:
    """无头浏览器HTML转图片工具 - 完整版本"""

    def __init__(self):
        self.output_dir = Path("images")
        self.output_dir.mkdir(exist_ok=True)
        self.last_process_time = None  # 记录上次处理时间

        # 优化：保存最佳尺寸配置，避免重复检测
        self.optimal_size_file = "data/optimal_browser_size.json"
        self.optimal_width = None
        self.optimal_height = None
        self.load_optimal_size()

    def setup_driver(self, width=None, height=None):
        """设置无头Chrome浏览器"""
        chrome_options = Options()
        chrome_options.add_argument('--headless=new')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--hide-scrollbars')
        chrome_options.add_argument('--enable-javascript')  # 启用JavaScript以加载图表
        chrome_options.add_argument('--mute-audio')
        chrome_options.add_argument('--force-device-scale-factor=1')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-popup-blocking')

        # 使用传入的尺寸或默认尺寸
        w = width or 1920
        h = height or 1080
        chrome_options.add_argument(f'--window-size={w},{h}')

        # 智能ChromeDriver设置
        try:
            # 首先尝试使用系统ChromeDriver
            if shutil.which('chromedriver'):
                print("  🔧 使用系统ChromeDriver")
                service = Service('chromedriver')
            else:
                print("  📥 下载ChromeDriver...")
                # 清理可能损坏的缓存
                import os
                cache_dir = os.path.expanduser("~/.wdm/drivers/chromedriver")
                if os.path.exists(cache_dir):
                    import shutil as shutil_module
                    shutil_module.rmtree(cache_dir, ignore_errors=True)

                # 重新下载
                service = Service(ChromeDriverManager().install())
        except Exception as e:
            print(f"  ⚠️ ChromeDriver设置失败: {e}")
            # 尝试使用默认路径
            service = Service()

        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_window_size(w, h)

        return driver

    def load_optimal_size(self):
        """加载保存的最佳浏览器尺寸"""
        try:
            if os.path.exists(self.optimal_size_file):
                with open(self.optimal_size_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.optimal_width = data.get('width', None)
                    self.optimal_height = data.get('height', None)
                    print(f"📐 已加载保存的最佳尺寸: {self.optimal_width}x{self.optimal_height}")
            else:
                print("📐 未找到保存的尺寸配置，将进行首次检测")
        except Exception as e:
            print(f"⚠️ 加载尺寸配置失败: {e}")
            self.optimal_width = None
            self.optimal_height = None

    def save_optimal_size(self, width, height):
        """保存最佳浏览器尺寸"""
        try:
            os.makedirs(os.path.dirname(self.optimal_size_file), exist_ok=True)
            data = {
                'width': width,
                'height': height,
                'last_updated': datetime.now().isoformat(),
                'note': 'PYTHIA图表最佳显示尺寸'
            }
            with open(self.optimal_size_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 已保存最佳尺寸配置: {width}x{height}")
        except Exception as e:
            print(f"⚠️ 保存尺寸配置失败: {e}")

    def reset_optimal_size(self):
        """重置最佳尺寸配置，强制重新检测"""
        self.optimal_width = None
        self.optimal_height = None
        try:
            if os.path.exists(self.optimal_size_file):
                os.remove(self.optimal_size_file)
                print("🔄 已重置尺寸配置，下次将重新检测")
        except Exception as e:
            print(f"⚠️ 重置尺寸配置失败: {e}")

    def wait_for_chart_loading(self, driver):
        """等待TradingView图表完全加载"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By

            wait = WebDriverWait(driver, 45)  # 增加WebDriverWait超时时间从30秒到45秒

            # 1. 等待TradingView脚本加载
            print("⏳ 等待TradingView脚本...")
            driver.execute_script("""
                var checkTradingView = function() {
                    return typeof TradingView !== 'undefined';
                };

                var startTime = Date.now();
                var checkInterval = setInterval(function() {
                    if (checkTradingView() || Date.now() - startTime > 30000) {  // 增加检查超时从20秒到30秒
                        clearInterval(checkInterval);
                        window.tradingViewLoaded = checkTradingView();
                    }
                }, 500);
            """)

            # 等待脚本检查完成
            WebDriverWait(driver, 35).until(  # 增加脚本检查超时时间从25秒到35秒
                lambda d: d.execute_script("return typeof window.tradingViewLoaded !== 'undefined'")
            )

            tv_loaded = driver.execute_script("return window.tradingViewLoaded")
            if tv_loaded:
                print("✅ TradingView脚本已加载")
            else:
                print("⚠️ TradingView脚本加载超时")

            # 2. 等待图表容器出现
            print("⏳ 等待图表容器...")
            try:
                wait.until(EC.presence_of_element_located((By.ID, "chart_container")))
                print("✅ 图表容器已找到")
            except:
                print("⚠️ 图表容器未找到，继续等待...")

            # 3. 等待图表内容渲染 - 检查iframe或canvas
            print("⏳ 等待图表内容渲染...")
            chart_rendered = False

            for attempt in range(20):  # 最多等待20秒
                try:
                    # 检查是否有图表内容
                    has_chart = driver.execute_script("""
                        var container = document.getElementById('chart_container');
                        if (!container) return false;

                        // 检查iframe（TradingView常用）
                        var iframe = container.querySelector('iframe');
                        if (iframe) return true;

                        // 检查canvas元素
                        var canvas = container.querySelector('canvas');
                        if (canvas) return true;

                        // 检查是否有实际内容
                        return container.children.length > 0 && container.offsetHeight > 100;
                    """)

                    if has_chart:
                        print("✅ 图表内容已渲染")
                        chart_rendered = True
                        break

                    time.sleep(2)  # 增加图表渲染检查间隔从1秒到2秒

                except Exception as e:
                    print(f"⚠️ 图表检测异常: {e}")
                    break

            if not chart_rendered:
                print("⚠️ 图表渲染检测超时，继续处理...")

            # 4. 深度检查图表是否完全渲染
            print("🔍 深度检查图表渲染状态...")
            self.verify_chart_fully_rendered(driver)

            # 5. 额外等待确保图表完全加载
            print("⏳ 最终等待图表稳定...")
            time.sleep(25)  # 增加等待时间从15秒到25秒
            print("✅ 图表加载等待完成")

        except Exception as e:
            print(f"⚠️ 图表加载等待失败: {e}")
            print("🔄 使用备用等待策略...")
            time.sleep(25)  # 增加备用等待时间从15秒到25秒

    def verify_chart_fully_rendered(self, driver):
        """深度验证图表是否完全渲染"""
        try:
            print("📊 验证图表完全渲染...")

            # 优化检查次数 - 从3轮减少到2轮
            for check_round in range(2):  # 进行2轮检查
                print(f"🔄 第 {check_round + 1}/2 轮渲染检查...")

                chart_status = driver.execute_script("""
                    var status = {
                        hasContainer: false,
                        hasIframe: false,
                        hasCanvas: false,
                        hasChartData: false,
                        containerSize: {width: 0, height: 0},
                        iframeLoaded: false,
                        canvasDrawn: false,
                        networkIdle: false
                    };

                    // 检查容器
                    var container = document.getElementById('chart_container');
                    if (container) {
                        status.hasContainer = true;
                        status.containerSize.width = container.offsetWidth;
                        status.containerSize.height = container.offsetHeight;

                        // 检查iframe
                        var iframe = container.querySelector('iframe');
                        if (iframe) {
                            status.hasIframe = true;
                            try {
                                // 检查iframe是否加载完成
                                if (iframe.contentDocument || iframe.contentWindow) {
                                    status.iframeLoaded = true;
                                }
                            } catch(e) {
                                // 跨域iframe，假设已加载
                                status.iframeLoaded = true;
                            }
                        }

                        // 检查canvas
                        var canvases = container.querySelectorAll('canvas');
                        if (canvases.length > 0) {
                            status.hasCanvas = true;

                            // 检查canvas是否有绘制内容
                            for (var i = 0; i < canvases.length; i++) {
                                var canvas = canvases[i];
                                if (canvas.width > 0 && canvas.height > 0) {
                                    try {
                                        var ctx = canvas.getContext('2d');
                                        var imageData = ctx.getImageData(0, 0, Math.min(canvas.width, 100), Math.min(canvas.height, 100));
                                        var data = imageData.data;

                                        // 检查是否有非透明像素
                                        for (var j = 3; j < data.length; j += 4) {
                                            if (data[j] > 0) {  // alpha > 0
                                                status.canvasDrawn = true;
                                                break;
                                            }
                                        }
                                        if (status.canvasDrawn) break;
                                    } catch(e) {
                                        // 无法访问canvas内容，假设已绘制
                                        status.canvasDrawn = true;
                                        break;
                                    }
                                }
                            }
                        }

                        // 检查是否有图表数据元素
                        var dataElements = container.querySelectorAll('[class*="chart"], [class*="plot"], [class*="series"], [id*="chart"], [id*="plot"]');
                        if (dataElements.length > 0) {
                            status.hasChartData = true;
                        }
                    }

                    // 检查网络请求是否完成（简单检查）
                    if (typeof performance !== 'undefined' && performance.getEntriesByType) {
                        var resources = performance.getEntriesByType('resource');
                        var pendingRequests = resources.filter(function(r) {
                            return r.responseEnd === 0;  // 未完成的请求
                        });
                        status.networkIdle = pendingRequests.length === 0;
                    }

                    return status;
                """)

                # 打印检查结果
                print(f"   📦 容器: {'✅' if chart_status['hasContainer'] else '❌'} ({chart_status['containerSize']['width']}x{chart_status['containerSize']['height']})")
                print(f"   🖼️ iframe: {'✅' if chart_status['hasIframe'] else '❌'} (加载: {'✅' if chart_status['iframeLoaded'] else '❌'})")
                print(f"   🎨 Canvas: {'✅' if chart_status['hasCanvas'] else '❌'} (绘制: {'✅' if chart_status['canvasDrawn'] else '❌'})")
                print(f"   📊 图表数据: {'✅' if chart_status['hasChartData'] else '❌'}")
                print(f"   🌐 网络空闲: {'✅' if chart_status['networkIdle'] else '❌'}")

                # 判断图表是否完全渲染
                chart_ready = (
                    chart_status['hasContainer'] and
                    chart_status['containerSize']['width'] > 100 and
                    chart_status['containerSize']['height'] > 100 and
                    (chart_status['hasIframe'] or chart_status['hasCanvas']) and
                    (chart_status['iframeLoaded'] or chart_status['canvasDrawn'])
                )

                if chart_ready:
                    print(f"   ✅ 第 {check_round + 1} 轮检查通过 - 图表已完全渲染")
                    break
                else:
                    print(f"   ⏳ 第 {check_round + 1} 轮检查未通过 - 继续等待...")
                    time.sleep(4)  # 增加检查间隔等待时间从2秒到4秒

            # 最终状态检查
            final_check = driver.execute_script("""
                var container = document.getElementById('chart_container');
                if (!container) return false;

                // 检查容器是否有足够的内容
                var hasContent = container.offsetWidth > 200 && container.offsetHeight > 200;
                var hasElements = container.children.length > 0;

                // 检查是否有可见的图表元素
                var visibleElements = 0;
                var allElements = container.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var el = allElements[i];
                    var style = window.getComputedStyle(el);
                    if (style.display !== 'none' && style.visibility !== 'hidden' &&
                        el.offsetWidth > 0 && el.offsetHeight > 0) {
                        visibleElements++;
                    }
                }

                return hasContent && hasElements && visibleElements > 5;
            """)

            if final_check:
                print("✅ 图表渲染验证通过 - 准备截图")
            else:
                print("⚠️ 图表渲染验证未完全通过 - 但继续截图")

        except Exception as e:
            print(f"⚠️ 图表渲染验证失败: {e}")
            print("🔄 跳过验证，继续截图...")

    def detect_content_size(self, driver):
        """智能检测页面内容的实际尺寸，去除多余空白"""
        try:
            # 更精确的内容尺寸检测
            dimensions = driver.execute_script("""
                // 获取所有可能的尺寸
                var body = document.body;
                var html = document.documentElement;

                // 获取实际内容区域
                var allElements = document.querySelectorAll('*');
                var maxRight = 0;
                var maxBottom = 0;
                var minLeft = window.innerWidth;
                var minTop = window.innerHeight;

                // 遍历所有元素找到实际内容边界
                for (var i = 0; i < allElements.length; i++) {
                    var el = allElements[i];
                    var rect = el.getBoundingClientRect();

                    // 跳过不可见元素
                    if (rect.width === 0 || rect.height === 0) continue;

                    maxRight = Math.max(maxRight, rect.right);
                    maxBottom = Math.max(maxBottom, rect.bottom);
                    minLeft = Math.min(minLeft, rect.left);
                    minTop = Math.min(minTop, rect.top);
                }

                // 计算实际内容尺寸
                var contentWidth = Math.max(maxRight - Math.max(0, minLeft), 1280);
                var contentHeight = Math.max(maxBottom - Math.max(0, minTop), 720);

                // 添加一些边距
                contentWidth = Math.min(contentWidth + 40, 1920);
                contentHeight = Math.min(contentHeight + 40, 1080);

                return {
                    width: Math.round(contentWidth),
                    height: Math.round(contentHeight),
                    bounds: {
                        left: minLeft,
                        top: minTop,
                        right: maxRight,
                        bottom: maxBottom
                    }
                };
            """)

            content_width = dimensions['width']
            content_height = dimensions['height']

            print(f"🔍 智能检测内容尺寸: {content_width}x{content_height}")
            print(f"📏 内容边界: left={dimensions['bounds']['left']:.1f}, right={dimensions['bounds']['right']:.1f}")

            return content_width, content_height

        except Exception as e:
            print(f"⚠️ 尺寸检测失败: {e}")
            return 1280, 720

    def convert_html_to_image(self, html_file_path, output_path=None, max_retries=3, auto_size=True):
        """转换HTML文件为图片，支持智能尺寸优化"""
        html_path = Path(html_file_path)
        if not html_path.exists():
            raise FileNotFoundError(f"HTML文件不存在: {html_file_path}")

        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.output_dir / f"{html_path.stem}_{timestamp}.png"

        print(f"📄 转换: {html_path.name}")
        print(f"📁 输出: {output_path}")

        for attempt in range(max_retries):
            driver = None
            try:
                print(f"🔄 尝试 {attempt + 1}/{max_retries}")

                # 智能尺寸选择：优先使用保存的最佳尺寸
                if self.optimal_width and self.optimal_height and auto_size:
                    print(f"🎯 使用保存的最佳尺寸: {self.optimal_width}x{self.optimal_height}")
                    driver = self.setup_driver(self.optimal_width, self.optimal_height)
                    need_size_detection = False
                else:
                    print("🔍 首次检测或使用默认尺寸")
                    driver = self.setup_driver(1920, 1080)
                    need_size_detection = auto_size

                # 加载HTML文件
                file_url = f"file://{html_path.absolute()}"
                print(f"🔗 加载: {file_url}")

                driver.get(file_url)

                # 等待页面加载和JavaScript执行
                print("⏳ 等待页面基础渲染...")
                time.sleep(8)  # 增加基础渲染等待时间从3秒到8秒

                # 等待TradingView图表完全加载
                print("📊 等待TradingView图表完全加载...")
                self.wait_for_chart_loading(driver)

                # 智能尺寸处理
                if need_size_detection:
                    # 首次检测：检测内容实际尺寸并保存
                    content_width, content_height = self.detect_content_size(driver)

                    # 保存最佳尺寸供下次使用
                    self.save_optimal_size(content_width, content_height)
                    self.optimal_width = content_width
                    self.optimal_height = content_height

                    # 关闭当前浏览器
                    driver.quit()

                    # 用检测到的尺寸重新启动浏览器
                    print(f"🔄 使用检测尺寸重新启动浏览器: {content_width}x{content_height}")
                    driver = self.setup_driver(content_width, content_height)

                    # 重新加载页面
                    driver.get(file_url)
                    time.sleep(8)  # 增加重新加载等待时间从3秒到8秒

                    # 重新等待图表完全加载
                    print("📊 重新等待图表完全加载...")
                    self.wait_for_chart_loading(driver)

                # 等待图表稳定
                print("⏰ 等待图表稳定...")
                time.sleep(90)  # 增加图表稳定等待时间从60秒到90秒
                print("✅ 图表稳定等待完成")

                # 额外的截图前稳定等待
                print("🎯 截图前最终稳定等待...")
                time.sleep(15)  # 新增15秒的截图前等待
                print("✅ 截图前稳定等待完成")

                # 截图
                print("📸 开始截图...")
                driver.save_screenshot(str(output_path))

                # 智能裁剪去除空白区域
                if auto_size:
                    try:
                        from PIL import Image
                        print("🔧 智能裁剪空白区域...")

                        with Image.open(output_path) as img:
                            # 转换为RGB模式以便处理
                            if img.mode != 'RGB':
                                img = img.convert('RGB')

                            # 获取图片尺寸
                            width, height = img.size
                            print(f"📐 原始尺寸: {width}x{height}")

                            # 检测实际内容边界（非黑色区域）
                            pixels = img.load()

                            # 找到左边界
                            left_bound = 0
                            for x in range(width):
                                has_content = False
                                for y in range(height):
                                    r, g, b = pixels[x, y]
                                    # 如果不是纯黑色或接近黑色
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    left_bound = max(0, x - 10)  # 留一点边距
                                    break

                            # 找到右边界
                            right_bound = width
                            for x in range(width-1, -1, -1):
                                has_content = False
                                for y in range(height):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    right_bound = min(width, x + 10)  # 留一点边距
                                    break

                            # 找到上边界
                            top_bound = 0
                            for y in range(height):
                                has_content = False
                                for x in range(width):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    top_bound = max(0, y - 10)
                                    break

                            # 找到下边界
                            bottom_bound = height
                            for y in range(height-1, -1, -1):
                                has_content = False
                                for x in range(width):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    bottom_bound = min(height, y + 10)
                                    break

                            # 裁剪图片
                            if right_bound > left_bound and bottom_bound > top_bound:
                                cropped_img = img.crop((left_bound, top_bound, right_bound, bottom_bound))
                                cropped_img.save(output_path, 'PNG')

                                new_width = right_bound - left_bound
                                new_height = bottom_bound - top_bound
                                print(f"✂️ 裁剪后尺寸: {new_width}x{new_height}")
                                print(f"📏 裁剪区域: ({left_bound}, {top_bound}) -> ({right_bound}, {bottom_bound})")
                            else:
                                print("⚠️ 未检测到有效内容区域，保持原图")

                    except ImportError:
                        print("⚠️ PIL不可用，无法进行智能裁剪")
                    except Exception as e:
                        print(f"⚠️ 智能裁剪失败: {e}")

                else:
                    # 非自动尺寸模式，强制调整到1280x720
                    try:
                        from PIL import Image
                        with Image.open(output_path) as img:
                            width, height = img.size
                            print(f"📐 图片尺寸: {width}x{height}")

                            if width != 1280 or height != 720:
                                print("🔧 调整图片尺寸到1280x720...")
                                resized_img = img.resize((1280, 720), Image.Resampling.LANCZOS)
                                resized_img.save(output_path, 'PNG')
                                print(f"✅ 已调整为: 1280x720")

                    except ImportError:
                        print("⚠️ PIL不可用，无法调整图片尺寸")

                print(f"✅ 成功: {output_path}")
                return str(output_path)

            except Exception as e:
                print(f"❌ 第 {attempt + 1} 次失败: {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(8)  # 增加重试间隔等待时间从3秒到8秒

            finally:
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass

    def send_to_telegram(self, image_path, html_filename, bot_token=None, chat_id=None, message_thread_id=None):
        """发送图片到Telegram群组或频道话题"""
        try:
            # 从参数获取配置，如果没有则从配置文件获取
            if not bot_token or not chat_id:
                try:
                    bot_token = OUTPUT_CONFIG["telegram"]["bot_token"]
                    chat_id = OUTPUT_CONFIG["telegram"]["chat_id"]
                    # 尝试从配置文件获取话题ID
                    message_thread_id = OUTPUT_CONFIG["telegram"].get("message_thread_id", message_thread_id)
                except KeyError:
                    print("⚠️ Telegram配置未设置，跳过发送")
                    return False

            if bot_token == "YOUR_BOT_TOKEN_HERE" or chat_id == "YOUR_CHAT_ID_HERE":
                print("⚠️ Telegram配置未设置，跳过发送")
                return False

            # 显示发送目标信息
            if message_thread_id:
                print(f"📤 发送图片到Telegram频道话题 (Thread ID: {message_thread_id})...")
            else:
                print("📤 发送图片到Telegram群组...")

            # 准备发送的消息
            caption = f"📊 Pythia分析图表\n📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            # 发送图片
            url = f"https://api.telegram.org/bot{bot_token}/sendPhoto"

            with open(image_path, 'rb') as photo:
                files = {'photo': photo}
                data = {
                    'chat_id': chat_id,
                    'caption': caption,
                    'parse_mode': 'HTML'
                }

                # 如果指定了话题ID，添加到请求数据中
                if message_thread_id:
                    data['message_thread_id'] = message_thread_id

                response = requests.post(url, files=files, data=data, timeout=30)

            if response.status_code == 200:
                if message_thread_id:
                    print(f"✅ 图片已成功发送到Telegram频道话题 (Thread ID: {message_thread_id})")
                else:
                    print("✅ 图片已成功发送到Telegram群组")
                return True
            else:
                print(f"❌ Telegram发送失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ Telegram发送异常: {e}")
            return False

    def send_to_telegram_topic(self, image_path, html_filename, topic_name="market_analysis"):
        """发送图片到Telegram频道的指定话题"""
        try:
            # 从配置文件获取话题配置
            topic_configs = OUTPUT_CONFIG["telegram"].get("topic_configs", {})

            if topic_name not in topic_configs:
                print(f"⚠️ 未找到话题配置: {topic_name}")
                print(f"📋 可用话题: {list(topic_configs.keys())}")
                # 回退到普通发送
                return self.send_to_telegram(image_path, html_filename)

            topic_config = topic_configs[topic_name]
            chat_id = topic_config.get("chat_id")
            thread_id = topic_config.get("thread_id")
            description = topic_config.get("description", topic_name)

            if not thread_id:
                print(f"⚠️ 话题 '{topic_name}' 未配置thread_id，使用普通发送")
                return self.send_to_telegram(image_path, html_filename, chat_id=chat_id)

            print(f"📤 发送到话题: {description} (Thread ID: {thread_id})")

            # 使用话题ID发送
            return self.send_to_telegram(
                image_path,
                html_filename,
                chat_id=chat_id,
                message_thread_id=thread_id
            )

        except Exception as e:
            print(f"❌ 话题发送异常: {e}")
            # 回退到普通发送
            return self.send_to_telegram(image_path, html_filename)

    def send_to_multiple_topics(self, image_path, html_filename, topic_names=None):
        """发送图片到多个Telegram话题"""
        if topic_names is None:
            topic_names = ["market_analysis"]  # 默认话题

        success_count = 0
        total_count = len(topic_names)

        for topic_name in topic_names:
            print(f"📤 发送到话题: {topic_name}")
            if self.send_to_telegram_topic(image_path, html_filename, topic_name):
                success_count += 1
                print(f"✅ 话题 '{topic_name}' 发送成功")
            else:
                print(f"❌ 话题 '{topic_name}' 发送失败")

        print(f"📊 发送结果: {success_count}/{total_count} 个话题发送成功")
        return success_count > 0

    def check_cooldown(self):
        """检查是否在冷却时间内"""
        if self.last_process_time is None:
            return False

        cooldown_minutes = 30
        time_since_last = datetime.now() - self.last_process_time
        cooldown_remaining = timedelta(minutes=cooldown_minutes) - time_since_last

        if cooldown_remaining.total_seconds() > 0:
            remaining_minutes = int(cooldown_remaining.total_seconds() / 60)
            remaining_seconds = int(cooldown_remaining.total_seconds() % 60)
            print(f"❄️ 冷却中... 还需等待 {remaining_minutes}分{remaining_seconds}秒")
            return True

        return False

    def find_latest_html(self):
        """查找最新的HTML文件"""
        data_dir = Path("data")
        if not data_dir.exists():
            print("❌ data目录不存在")
            return None

        html_files = list(data_dir.glob("*.html"))
        if not html_files:
            print("❌ 没有找到HTML文件")
            return None

        latest_file = max(html_files, key=lambda f: f.stat().st_mtime)
        mod_time = datetime.fromtimestamp(latest_file.stat().st_mtime)

        print(f"📁 最新: {latest_file.name}")
        print(f"📅 时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")

        return latest_file


class PythiaIntegratedAnalyzer:
    """PYTHIA代币全面分析与报告发送器 - 完整整合版本"""

    def __init__(self):
        # 从配置文件加载设置
        self.base_url = API_CONFIG["base_url"]
        self.chain_id = PYTHIA_TOKEN_INFO["chain"]
        self.pythia_token_address = PYTHIA_TOKEN_INFO["contract_address"]
        self.timeout = API_CONFIG.get("timeout", 30)  # 增加超时时间到30秒
        self.retry_attempts = API_CONFIG.get("retry_attempts", 5)  # 增加重试次数
        self.retry_delay = API_CONFIG.get("retry_delay", 3)  # 增加重试间隔

        # API速率限制 (每分钟)
        self.rate_limits = API_CONFIG["rate_limits"]

        # 历史数据存储
        self.price_history = []
        self.volume_history = []
        self.last_update = None

        # 监控状态
        self.monitoring = False
        self.monitor_thread = None

        # 初始化HTML转图片转换器
        self.html_converter = HeadlessHTMLConverter()

        # Telegram配置
        try:
            self.telegram_bot_token = OUTPUT_CONFIG["telegram"]["bot_token"]
            self.telegram_chat_id = OUTPUT_CONFIG["telegram"]["chat_id"]
            self.auto_group_mode = OUTPUT_CONFIG["telegram"].get("auto_group_mode", False)
            self.fallback_chat_id = OUTPUT_CONFIG["telegram"].get("fallback_chat_id", None)
            self.active_groups = set()  # 存储活跃的群组ID
        except KeyError as e:
            print(f"⚠️ 配置文件中缺少Telegram配置项: {e}")
            self.telegram_bot_token = None
            self.telegram_chat_id = None
            self.auto_group_mode = False
            self.fallback_chat_id = None
            self.active_groups = set()

        # 添加缓存机制
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存

        # 群组活动记录文件
        self.active_groups_file = "data/active_groups.json"
        self.load_active_groups()

        # 创建持久化会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Connection': 'keep-alive'
        })

    def load_active_groups(self):
        """加载活跃群组列表"""
        try:
            if os.path.exists(self.active_groups_file):
                with open(self.active_groups_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.active_groups = set(data.get('groups', []))
                    print(f"📱 已加载 {len(self.active_groups)} 个活跃群组")
            else:
                self.active_groups = set()
                print("📱 未找到活跃群组记录，将自动检测")
        except Exception as e:
            print(f"⚠️ 加载活跃群组失败: {e}")
            self.active_groups = set()

    def save_active_groups(self):
        """保存活跃群组列表"""
        try:
            os.makedirs(os.path.dirname(self.active_groups_file), exist_ok=True)
            data = {
                'groups': list(self.active_groups),
                'last_updated': datetime.now().isoformat()
            }
            with open(self.active_groups_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存活跃群组失败: {e}")

    def add_active_group(self, chat_id: str):
        """添加活跃群组"""
        if chat_id and chat_id != "AUTO_DETECT":
            self.active_groups.add(str(chat_id))
            self.save_active_groups()
            print(f"📱 已添加活跃群组: {chat_id}")

    def get_target_chat_ids(self):
        """获取目标聊天ID列表"""
        chat_ids = []

        if self.auto_group_mode:
            # 自动群组模式：使用所有活跃群组
            if self.active_groups:
                chat_ids.extend(list(self.active_groups))
                print(f"🎯 自动群组模式：将发送到 {len(self.active_groups)} 个活跃群组")
            elif self.fallback_chat_id:
                chat_ids.append(self.fallback_chat_id)
                print(f"🎯 使用备用群组: {self.fallback_chat_id}")
        else:
            # 传统模式：使用配置的chat_id
            if self.telegram_chat_id and self.telegram_chat_id != "AUTO_DETECT":
                chat_ids.append(self.telegram_chat_id)
                print(f"🎯 传统模式：发送到配置群组 {self.telegram_chat_id}")

        return chat_ids

    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """发送API请求 - 优化版本带缓存和连接复用"""
        import ssl
        from urllib3.exceptions import SSLError
        import hashlib

        # 生成缓存键
        cache_key = f"{endpoint}_{str(params)}"
        cache_hash = hashlib.md5(cache_key.encode()).hexdigest()

        # 检查缓存
        if cache_hash in self.cache:
            cache_data, cache_time = self.cache[cache_hash]
            if time.time() - cache_time < self.cache_timeout:
                print(f"📋 使用缓存数据: {endpoint}")
                return cache_data

        for attempt in range(self.retry_attempts):
            try:
                url = f"{self.base_url}{endpoint}"

                # 使用持久化会话
                response = self.session.get(
                    url,
                    params=params,
                    timeout=self.timeout,
                    verify=True  # 启用SSL验证
                )

                if response.status_code == 200:
                    result = response.json()
                    # 缓存成功的响应
                    self.cache[cache_hash] = (result, time.time())
                    return result
                elif response.status_code == 429:  # 速率限制
                    wait_time = self.retry_delay * (attempt + 1)  # 递增等待时间
                    print(f"⚠️ 触发速率限制，等待{wait_time}秒...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"❌ API请求失败: {response.status_code} - {response.text}")

            except (requests.exceptions.Timeout, requests.exceptions.ReadTimeout) as e:
                wait_time = self.retry_delay * (attempt + 1)
                print(f"❌ 请求超时 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)

            except (requests.exceptions.SSLError, SSLError, ssl.SSLError) as e:
                wait_time = self.retry_delay * (attempt + 1)
                print(f"❌ SSL连接错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)

            except requests.exceptions.ConnectionError as e:
                wait_time = self.retry_delay * (attempt + 1)
                print(f"❌ 连接错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)

            except Exception as e:
                wait_time = self.retry_delay * (attempt + 1)
                print(f"❌ 未知错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)

        print(f"❌ 所有重试尝试失败，跳过此请求")
        return None

    def search_pythia_pairs(self, query: str = "PYTHIA") -> Optional[Dict]:
        """搜索PYTHIA相关的交易对"""
        endpoint = "/latest/dex/search"
        params = {"q": query}

        print(f"🔍 搜索PYTHIA相关交易对...")
        return self._make_request(endpoint, params)

    def get_token_pairs(self, token_address: str = None) -> Optional[Dict]:
        """获取指定代币的所有交易对"""
        if not token_address:
            token_address = self.pythia_token_address

        endpoint = f"/latest/dex/tokens/{self.chain_id}/{token_address}"

        print(f"📊 获取代币交易对数据...")
        return self._make_request(endpoint)

    def get_specific_pair(self, pair_address: str) -> Optional[Dict]:
        """获取特定交易对的详细信息"""
        endpoint = f"/latest/dex/pairs/{self.chain_id}/{pair_address}"

        print(f"🎯 获取交易对详细信息...")
        return self._make_request(endpoint)

    def analyze_price_data(self, pair_data: Dict) -> Dict[str, Any]:
        """分析价格数据"""
        if not pair_data or "pairs" not in pair_data:
            return {}

        analysis = {}

        for pair in pair_data["pairs"]:
            pair_address = pair.get("pairAddress", "unknown")

            # 基础信息
            analysis[pair_address] = {
                "dex": pair.get("dexId", "unknown"),
                "base_token": pair.get("baseToken", {}).get("symbol", "unknown"),
                "quote_token": pair.get("quoteToken", {}).get("symbol", "unknown"),
                "price_usd": float(pair.get("priceUsd", 0)),
                "price_native": pair.get("priceNative", "0"),
                "market_cap": pair.get("marketCap", 0),
                "fdv": pair.get("fdv", 0),
                "liquidity_usd": pair.get("liquidity", {}).get("usd", 0),
                "volume_24h": pair.get("volume", {}).get("h24", 0),
                "price_change_24h": pair.get("priceChange", {}).get("h24", 0),
                "txns_24h": pair.get("txns", {}).get("h24", {}),
                "pair_created_at": pair.get("pairCreatedAt", 0)
            }

        return analysis

    def analyze_market_sentiment(self, pairs_data: List[Dict]) -> Dict[str, Any]:
        """分析市场情绪"""
        if not pairs_data:
            return {"sentiment": "unknown", "score": 0}

        total_volume = 0
        weighted_price_change = 0
        total_buys = 0
        total_sells = 0

        for pair in pairs_data:
            volume = pair.get("volume", {}).get("h24", 0)
            price_change = pair.get("priceChange", {}).get("h24", 0)
            txns = pair.get("txns", {}).get("h24", {})

            total_volume += volume
            weighted_price_change += price_change * volume
            total_buys += txns.get("buys", 0)
            total_sells += txns.get("sells", 0)

        # 计算加权平均价格变化
        avg_price_change = weighted_price_change / total_volume if total_volume > 0 else 0

        # 计算买卖比例
        buy_sell_ratio = total_buys / total_sells if total_sells > 0 else float('inf')

        # 情绪评分 (0-100)
        sentiment_score = 50  # 基础分数
        sentiment_score += avg_price_change * 2  # 价格变化影响
        sentiment_score += min((buy_sell_ratio - 1) * 10, 20)  # 买卖比例影响
        sentiment_score = max(0, min(100, sentiment_score))  # 限制在0-100

        # 情绪分类
        if sentiment_score >= 80:
            sentiment = "🚀 极度乐观"
        elif sentiment_score >= 65:
            sentiment = "📈 乐观"
        elif sentiment_score >= 50:
            sentiment = "😐 中性"
        elif sentiment_score >= 35:
            sentiment = "📉 悲观"
        else:
            sentiment = "🔻 极度悲观"

        return {
            "sentiment": sentiment,
            "score": sentiment_score,
            "avg_price_change": avg_price_change,
            "buy_sell_ratio": buy_sell_ratio,
            "total_volume": total_volume
        }

    def filter_pairs_by_criteria(self, pairs: List[Dict]) -> tuple[List[Dict], Dict]:
        """根据配置的标准过滤交易对 - 只过滤交易量和流动性"""

        # 简单过滤条件：只过滤交易量和流动性
        min_liquidity = 10000      # 最小流动性1万美元
        min_volume_24h = 10000     # 最小24小时交易量1万美元

        filtered_pairs = []
        filter_stats = {
            "total_pairs": len(pairs),
            "filtered_by_market_cap": 0,
            "filtered_by_liquidity": 0,
            "filtered_by_volume": 0,
            "filtered_by_price": 0,
            "filtered_by_transactions": 0,
            "filtered_by_price_change": 0,
            "filtered_by_age": 0,
            "filtered_by_token_info": 0,
            "filtered_by_dex": 0,
            "filtered_by_quality": 0,
            "final_pairs": 0
        }

        for pair in pairs:
            # 获取关键数据
            liquidity = pair.get("liquidity", {}).get("usd", 0)
            volume_24h = pair.get("volume", {}).get("h24", 0)

            # 1. 流动性过滤
            if liquidity < min_liquidity:
                filter_stats["filtered_by_liquidity"] += 1
                continue

            # 2. 交易量过滤
            if volume_24h < min_volume_24h:
                filter_stats["filtered_by_volume"] += 1
                continue

            # 通过过滤条件
            filtered_pairs.append(pair)

        filter_stats["final_pairs"] = len(filtered_pairs)

        print(f"💎 简单过滤完成:")
        print(f"   流动性阈值: ${min_liquidity:,}")
        print(f"   交易量阈值: ${min_volume_24h:,}")
        print(f"   原始交易对: {filter_stats['total_pairs']}")
        print(f"   过滤后交易对: {filter_stats['final_pairs']}")
        print(f"   流动性过滤: {filter_stats['filtered_by_liquidity']}个")
        print(f"   交易量过滤: {filter_stats['filtered_by_volume']}个")

        return filtered_pairs, filter_stats

        # 高级过滤条件
        min_transaction_count = filter_config.get("min_transaction_count", 50)
        max_price_change = filter_config.get("max_price_change", 500)
        min_age_hours = filter_config.get("min_age_hours", 24)
        require_both_tokens = filter_config.get("require_both_tokens", True)

        # 质量评分系统
        enable_quality_scoring = filter_config.get("enable_quality_scoring", True)
        min_quality_score = filter_config.get("min_quality_score", 60)

        # DEX白名单
        trusted_dexes = filter_config.get("trusted_dexes", [])
        enable_dex_whitelist = filter_config.get("enable_dex_whitelist", True)

        filtered_pairs = []
        filter_stats = {
            "total_pairs": len(pairs),
            "filtered_by_market_cap": 0,
            "filtered_by_liquidity": 0,
            "filtered_by_volume": 0,
            "filtered_by_price": 0,
            "filtered_by_transactions": 0,
            "filtered_by_price_change": 0,
            "filtered_by_age": 0,
            "filtered_by_token_info": 0,
            "filtered_by_dex": 0,
            "filtered_by_quality": 0,
            "final_pairs": 0
        }

        for pair in pairs:
            # 获取关键数据
            market_cap = pair.get("marketCap", 0)
            liquidity = pair.get("liquidity", {}).get("usd", 0)
            volume_24h = pair.get("volume", {}).get("h24", 0)

            # 安全地转换价格
            try:
                price_str = pair.get("priceUsd", "0")
                price = float(price_str) if price_str else 0
            except (ValueError, TypeError):
                price = 0

            price_change_24h = pair.get("priceChange", {}).get("h24", 0)
            txns_24h = pair.get("txns", {}).get("h24", {})
            dex_id = pair.get("dexId", "").lower()

            # 获取交易笔数
            total_txns = txns_24h.get("buys", 0) + txns_24h.get("sells", 0)

            # 获取代币信息
            base_token = pair.get("baseToken", {})
            quote_token = pair.get("quoteToken", {})

            # 获取交易对创建时间
            pair_created_at = pair.get("pairCreatedAt", 0)

            # 1. 基础过滤条件
            if market_cap < min_market_cap:
                filter_stats["filtered_by_market_cap"] += 1
                continue

            if liquidity < min_liquidity:
                filter_stats["filtered_by_liquidity"] += 1
                continue

            if volume_24h < min_volume_24h:
                filter_stats["filtered_by_volume"] += 1
                continue

            if price < min_price or price > max_price:
                filter_stats["filtered_by_price"] += 1
                continue

            # 2. 高级过滤条件
            if total_txns < min_transaction_count:
                filter_stats["filtered_by_transactions"] += 1
                continue

            if abs(price_change_24h) > max_price_change:
                filter_stats["filtered_by_price_change"] += 1
                continue

            # 3. 交易对年龄过滤
            if pair_created_at > 0:
                import time
                current_time = time.time()
                pair_age_hours = (current_time - pair_created_at) / 3600
                if pair_age_hours < min_age_hours:
                    filter_stats["filtered_by_age"] += 1
                    continue

            # 4. 代币信息完整性检查
            if require_both_tokens:
                if not (base_token.get("symbol") and quote_token.get("symbol")):
                    filter_stats["filtered_by_token_info"] += 1
                    continue

            # 5. DEX白名单过滤
            if enable_dex_whitelist and trusted_dexes:
                if dex_id not in trusted_dexes:
                    filter_stats["filtered_by_dex"] += 1
                    continue

            # 6. 质量评分过滤
            if enable_quality_scoring:
                quality_score = self.calculate_pair_quality_score(pair)
                if quality_score < min_quality_score:
                    filter_stats["filtered_by_quality"] += 1
                    continue

            # 通过所有过滤条件
            filtered_pairs.append(pair)

        filter_stats["final_pairs"] = len(filtered_pairs)
        return filtered_pairs, filter_stats

    def calculate_pair_quality_score(self, pair: Dict) -> float:
        """计算交易对质量评分 (0-100分)"""
        score = 0.0

        # 获取数据
        market_cap = pair.get("marketCap", 0)
        liquidity = pair.get("liquidity", {}).get("usd", 0)
        volume_24h = pair.get("volume", {}).get("h24", 0)
        price_change_24h = abs(pair.get("priceChange", {}).get("h24", 0))
        txns_24h = pair.get("txns", {}).get("h24", {})
        dex_id = pair.get("dexId", "").lower()

        total_txns = txns_24h.get("buys", 0) + txns_24h.get("sells", 0)
        buys = txns_24h.get("buys", 0)
        sells = txns_24h.get("sells", 0)

        # 1. 市值评分 (0-20分)
        if market_cap >= 10000000:  # 1000万+
            score += 20
        elif market_cap >= 1000000:  # 100万+
            score += 15
        elif market_cap >= 500000:   # 50万+
            score += 10
        elif market_cap >= 100000:   # 10万+
            score += 5

        # 2. 流动性评分 (0-20分)
        if liquidity >= 1000000:     # 100万+
            score += 20
        elif liquidity >= 500000:    # 50万+
            score += 15
        elif liquidity >= 100000:    # 10万+
            score += 10
        elif liquidity >= 50000:     # 5万+
            score += 5

        # 3. 交易量评分 (0-20分)
        if volume_24h >= 1000000:    # 100万+
            score += 20
        elif volume_24h >= 500000:   # 50万+
            score += 15
        elif volume_24h >= 100000:   # 10万+
            score += 10
        elif volume_24h >= 10000:    # 1万+
            score += 5

        # 4. 交易活跃度评分 (0-15分)
        if total_txns >= 1000:       # 1000笔+
            score += 15
        elif total_txns >= 500:      # 500笔+
            score += 12
        elif total_txns >= 200:      # 200笔+
            score += 8
        elif total_txns >= 50:       # 50笔+
            score += 5

        # 5. 价格稳定性评分 (0-10分) - 价格变化越小越好
        if price_change_24h <= 5:    # 5%以内
            score += 10
        elif price_change_24h <= 15: # 15%以内
            score += 7
        elif price_change_24h <= 30: # 30%以内
            score += 5
        elif price_change_24h <= 50: # 50%以内
            score += 2

        # 6. 买卖平衡评分 (0-10分)
        if sells > 0:
            buy_sell_ratio = buys / sells
            if 0.8 <= buy_sell_ratio <= 1.2:  # 平衡
                score += 10
            elif 0.6 <= buy_sell_ratio <= 1.4:  # 较平衡
                score += 7
            elif 0.4 <= buy_sell_ratio <= 1.6:  # 一般
                score += 4

        # 7. DEX信誉评分 (0-5分)
        premium_dexes = ["uniswap", "pancakeswap", "sushiswap"]
        good_dexes = ["quickswap", "traderjoe", "spookyswap", "raydium"]

        if dex_id in premium_dexes:
            score += 5
        elif dex_id in good_dexes:
            score += 3
        else:
            score += 1

        return min(score, 100.0)  # 确保不超过100分

    def search_all_pythia_pairs(self) -> tuple[List[Dict], Dict]:
        """搜索所有PYTHIA相关交易对并应用过滤 - 优化版本"""
        import concurrent.futures
        import threading

        all_pairs = []
        search_keywords = SEARCH_KEYWORDS
        successful_searches = 0
        total_searches = len(search_keywords)

        # 使用线程池并行搜索，但控制并发数避免速率限制
        max_workers = min(3, len(search_keywords))  # 最多3个并发请求

        def search_keyword(keyword):
            """单个关键词搜索函数"""
            try:
                print(f"🔍 搜索关键词: {keyword}")
                results = self.search_pythia_pairs(keyword)

                if results and "pairs" in results:
                    pairs = results["pairs"]
                    print(f"   找到 {len(pairs)} 个交易对")
                    return pairs, True
                else:
                    print(f"   ⚠️ 搜索失败，跳过关键词: {keyword}")
                    return [], False
            except Exception as e:
                print(f"   ❌ 搜索异常: {keyword} - {e}")
                return [], False

        # 并行执行搜索
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有搜索任务
            future_to_keyword = {
                executor.submit(search_keyword, keyword): keyword
                for keyword in search_keywords
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_keyword):
                keyword = future_to_keyword[future]
                try:
                    pairs, success = future.result(timeout=30)  # 30秒超时
                    if success:
                        all_pairs.extend(pairs)
                        successful_searches += 1
                except concurrent.futures.TimeoutError:
                    print(f"   ⏰ 搜索超时: {keyword}")
                except Exception as e:
                    print(f"   ❌ 搜索异常: {keyword} - {e}")

                # 短暂延迟避免过快请求
                time.sleep(0.3)

        print(f"📈 搜索完成: {successful_searches}/{total_searches} 个关键词成功")

        # 如果没有任何成功的搜索，尝试备用策略
        if not all_pairs:
            print("🔄 启用备用搜索策略...")
            backup_result = self.search_pythia_pairs("PYTHIA")  # 只搜索主要关键词
            if backup_result and "pairs" in backup_result:
                all_pairs.extend(backup_result["pairs"])
                print(f"   备用搜索找到 {len(backup_result['pairs'])} 个交易对")

        # 去重
        unique_pairs = {}
        for pair in all_pairs:
            pair_addr = pair.get("pairAddress")
            if pair_addr and pair_addr not in unique_pairs:
                unique_pairs[pair_addr] = pair

        raw_pairs = list(unique_pairs.values())

        # 应用过滤条件
        filtered_pairs, filter_stats = self.filter_pairs_by_criteria(raw_pairs)

        print(f"📊 高质量交易对过滤结果:")
        print(f"   原始交易对: {filter_stats['total_pairs']}")
        print(f"   过滤后交易对: {filter_stats['final_pairs']}")
        print(f"   过滤率: {((filter_stats['total_pairs'] - filter_stats['final_pairs']) / filter_stats['total_pairs'] * 100):.1f}%")

        # 显示详细过滤统计
        if filter_stats['total_pairs'] > 0:
            print(f"   📋 过滤详情:")
            print(f"      市值过滤: {filter_stats.get('filtered_by_market_cap', 0)}")
            print(f"      流动性过滤: {filter_stats.get('filtered_by_liquidity', 0)}")
            print(f"      交易量过滤: {filter_stats.get('filtered_by_volume', 0)}")
            print(f"      价格过滤: {filter_stats.get('filtered_by_price', 0)}")
            print(f"      交易笔数过滤: {filter_stats.get('filtered_by_transactions', 0)}")
            print(f"      价格波动过滤: {filter_stats.get('filtered_by_price_change', 0)}")
            print(f"      DEX过滤: {filter_stats.get('filtered_by_dex', 0)}")
            print(f"      质量评分过滤: {filter_stats.get('filtered_by_quality', 0)}")

        # 性能提升提示
        if filter_stats['final_pairs'] < filter_stats['total_pairs'] * 0.5:
            improvement = (filter_stats['total_pairs'] - filter_stats['final_pairs']) / filter_stats['total_pairs'] * 100
            print(f"   🚀 性能提升: 减少了{improvement:.0f}%的数据处理量")

        return filtered_pairs, filter_stats

    def calculate_comprehensive_metrics(self, pairs_data: List[Dict], analysis: Dict) -> Dict:
        """计算综合市场指标"""
        metrics = {
            'total_pairs': len(pairs_data),
            'total_volume': 0,
            'total_liquidity': 0,
            'total_market_cap': 0,
            'total_buys': 0,
            'total_sells': 0,
            'price_range': {'min': float('inf'), 'max': 0},
            'dex_distribution': {},
            'positive_pairs': 0,
            'negative_pairs': 0,
            'neutral_pairs': 0,
            'highest_volume_pair': None,
            'highest_volume': 0
        }

        for pair in pairs_data:
            # 交易量和流动性
            volume = pair.get('volume', {}).get('h24', 0)
            liquidity = pair.get('liquidity', {}).get('usd', 0)
            market_cap = pair.get('marketCap', 0)
            price = float(pair.get('priceUsd', 0))
            price_change = pair.get('priceChange', {}).get('h24', 0)

            metrics['total_volume'] += volume
            metrics['total_liquidity'] += liquidity

            # 找到交易量最高的交易对，使用其市值作为标准
            if volume > metrics['highest_volume']:
                metrics['highest_volume'] = volume
                metrics['highest_volume_pair'] = pair
                if market_cap > 0:
                    metrics['total_market_cap'] = market_cap

            # 价格范围 - 过滤异常价格数据
            if price > 0.01 and price < 1000:
                metrics['price_range']['min'] = min(metrics['price_range']['min'], price)
                metrics['price_range']['max'] = max(metrics['price_range']['max'], price)

            # 买卖统计
            txns = pair.get('txns', {}).get('h24', {})
            metrics['total_buys'] += txns.get('buys', 0)
            metrics['total_sells'] += txns.get('sells', 0)

            # DEX分布
            dex = pair.get('dexId', 'unknown')
            metrics['dex_distribution'][dex] = metrics['dex_distribution'].get(dex, 0) + 1

            # 价格变化分布
            if price_change > 1:
                metrics['positive_pairs'] += 1
            elif price_change < -1:
                metrics['negative_pairs'] += 1
            else:
                metrics['neutral_pairs'] += 1

        return metrics

    def generate_fallback_report(self) -> str:
        """生成备用报告 - 当网络请求失败时使用"""
        now = datetime.now()

        fallback_html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告 - 网络异常</title>
    <style>
        body {{
            font-family: 'Arial', sans-serif;
            background: #0D1117;
            color: #E6EDF3;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }}
        .error-container {{
            background: #161B22;
            border: 1px solid #30363D;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
        }}
        .error-icon {{
            font-size: 4rem;
            margin-bottom: 20px;
        }}
        .error-title {{
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #F59E0B;
        }}
        .error-message {{
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #B0BAC6;
        }}
        .retry-info {{
            background: #0D1117;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #7D8590;
        }}
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">🔌</div>
        <h1 class="error-title">网络连接异常</h1>
        <p class="error-message">
            无法连接到DexScreener API服务器。<br>
            这可能是由于网络连接问题或API服务暂时不可用。
        </p>
        <div class="retry-info">
            <strong>生成时间:</strong> {now.strftime('%Y-%m-%d %H:%M:%S')}<br>
            <strong>状态:</strong> 自动重试机制已启用<br>
            <strong>建议:</strong> 系统将在下个周期自动重试
        </div>
    </div>
</body>
</html>"""

        return fallback_html

    def save_api_data_to_txt(self, pairs_data: List[Dict], analysis: Dict, sentiment: Dict, metrics: Dict) -> str:
        """保存API获取的原始数据到txt文件 - AI优化格式"""
        try:
            # 确保data_txt目录存在
            data_txt_dir = "data_txt"
            if not os.path.exists(data_txt_dir):
                os.makedirs(data_txt_dir)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            txt_filename = f"pythia_data_{timestamp}.txt"
            txt_filepath = os.path.join(data_txt_dir, txt_filename)

            # AI优化的数据格式 - 结构化且简洁
            with open(txt_filepath, 'w', encoding='utf-8') as f:
                # 时间戳
                f.write(f"TIMESTAMP: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # 核心指标 - 单行格式便于AI解析
                f.write("MARKET_SUMMARY:\n")
                f.write(f"total_pairs={len(pairs_data)}\n")
                f.write(f"market_cap={metrics.get('total_market_cap', 0):.2f}\n")
                f.write(f"volume_24h={metrics.get('total_volume', 0):.2f}\n")
                f.write(f"liquidity={metrics.get('total_liquidity', 0):.2f}\n")
                f.write(f"buys={metrics.get('total_buys', 0)}\n")
                f.write(f"sells={metrics.get('total_sells', 0)}\n")
                f.write(f"buy_sell_ratio={metrics.get('total_buys', 0) / max(metrics.get('total_sells', 1), 1):.3f}\n")

                # 情绪分析
                f.write(f"sentiment_score={sentiment.get('score', 0):.1f}\n")
                f.write(f"sentiment_text={sentiment.get('sentiment', 'unknown')}\n")
                f.write(f"avg_price_change={sentiment.get('avg_price_change', 0):.3f}\n\n")

                # 价格范围
                price_range = metrics.get('price_range', {})
                if price_range.get('min') != float('inf'):
                    f.write("PRICE_RANGE:\n")
                    f.write(f"min_price={price_range.get('min', 0):.8f}\n")
                    f.write(f"max_price={price_range.get('max', 0):.8f}\n")
                    f.write(f"price_variance={((price_range.get('max', 0) - price_range.get('min', 0)) / price_range.get('min', 1) * 100):.3f}\n\n")

                # DEX分布 - 简洁格式
                dex_distribution = metrics.get('dex_distribution', {})
                if dex_distribution:
                    f.write("DEX_DISTRIBUTION:\n")
                    for dex, count in sorted(dex_distribution.items(), key=lambda x: x[1], reverse=True):
                        percentage = (count / len(pairs_data)) * 100
                        f.write(f"{dex}={count}({percentage:.1f}%)\n")
                    f.write("\n")

                # 交易对详细数据 - 结构化格式
                f.write("TRADING_PAIRS:\n")
                for i, pair in enumerate(pairs_data):
                    dex = pair.get('dexId', 'unknown')
                    base_symbol = pair.get('baseToken', {}).get('symbol', 'UNK')
                    quote_symbol = pair.get('quoteToken', {}).get('symbol', 'UNK')
                    price = float(pair.get('priceUsd', 0))
                    market_cap = pair.get('marketCap', 0)
                    liquidity = pair.get('liquidity', {}).get('usd', 0)
                    volume_24h = pair.get('volume', {}).get('h24', 0)
                    price_change = pair.get('priceChange', {}).get('h24', 0)
                    pair_address = pair.get('pairAddress', 'unknown')

                    # 交易数据
                    txns_24h = pair.get('txns', {}).get('h24', {})
                    buys_24h = txns_24h.get('buys', 0)
                    sells_24h = txns_24h.get('sells', 0)

                    f.write(f"pair_{i+1}:\n")
                    f.write(f"  dex={dex}\n")
                    f.write(f"  symbol={base_symbol}/{quote_symbol}\n")
                    f.write(f"  address={pair_address}\n")
                    f.write(f"  price_usd={price:.8f}\n")
                    f.write(f"  market_cap={market_cap:.2f}\n")
                    f.write(f"  liquidity_usd={liquidity:.2f}\n")
                    f.write(f"  volume_24h={volume_24h:.2f}\n")
                    f.write(f"  price_change_24h={price_change:.3f}\n")
                    f.write(f"  buys_24h={buys_24h}\n")
                    f.write(f"  sells_24h={sells_24h}\n")
                    f.write(f"  created_at={pair.get('pairCreatedAt', 0)}\n")

                f.write("\n")

                # 数据导出完成 - 不包含原始数据源

            print(f"✅ AI优化数据已保存: {txt_filepath}")
            return txt_filepath

        except Exception as e:
            print(f"❌ 保存AI优化数据失败: {e}")
            return ""

    def save_report_to_file(self, report_content: str) -> str:
        """保存报告到文件，并自动转换为图片发送到Telegram"""
        try:
            # 确保data目录存在
            data_dir = OUTPUT_CONFIG.get("data_directory", "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            # 生成文件名 - 现在保存为HTML格式
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pythia_analysis_{timestamp}.html"
            filepath = os.path.join(data_dir, filename)

            # 保存HTML文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)

            print(f"✅ HTML报告已保存: {filepath}")

            # 自动转换HTML为图片
            try:
                print("🔄 开始将HTML转换为图片...")
                image_path = self.html_converter.convert_html_to_image(filepath)

                if image_path:
                    print(f"✅ 图片转换成功: {image_path}")

                    # 发送图片到Telegram群组
                    if ENABLE_TELEGRAM_SEND and self.telegram_bot_token:
                        target_chat_ids = self.get_target_chat_ids()

                        if target_chat_ids:
                            success_count = 0
                            for chat_id in target_chat_ids:
                                success = self.html_converter.send_to_telegram(
                                    image_path, filename, self.telegram_bot_token, chat_id
                                )
                                if success:
                                    success_count += 1
                                    print(f"✅ 图片已成功发送到群组: {chat_id}")
                                else:
                                    print(f"❌ 发送到群组 {chat_id} 失败")

                            if success_count > 0:
                                print(f"🎉 图片已成功发送到 {success_count}/{len(target_chat_ids)} 个群组")
                            else:
                                print("❌ 所有群组发送失败")
                        else:
                            print("⚠️ 未找到目标群组，跳过发送")
                    else:
                        if not ENABLE_TELEGRAM_SEND:
                            print("🔇 Telegram发送已关闭")
                        else:
                            print("⚠️ Telegram Bot Token未设置，跳过发送")
                else:
                    print("❌ 图片转换失败")

            except Exception as e:
                print(f"❌ HTML转图片过程出错: {e}")

            return filepath

        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
            return ""

    def generate_professional_report(self) -> str:
        """生成专业级PYTHIA代币分析报告 - 使用完整专业模板"""
        print("🐭 开始生成PYTHIA专业分析报告...")
        print("=" * 60)

        # 获取并过滤数据
        pairs_data, filter_stats = self.search_all_pythia_pairs()
        if not pairs_data:
            print("⚠️ 无法获取实时数据，生成备用报告...")
            return self.generate_fallback_report()

        formatted_data = {"pairs": pairs_data}
        analysis = self.analyze_price_data(formatted_data)
        sentiment = self.analyze_market_sentiment(pairs_data)
        metrics = self.calculate_comprehensive_metrics(pairs_data, analysis)

        # 保存AI优化数据到txt文件
        print("💾 保存AI优化数据到data_txt文件夹...")
        txt_filepath = self.save_api_data_to_txt(pairs_data, analysis, sentiment, metrics)
        if txt_filepath:
            print(f"✅ AI优化数据已保存: {txt_filepath}")

        # 开始生成报告
        now = datetime.now()

        # 计算关键指标
        total_market_cap = metrics['total_market_cap']
        total_volume = metrics['total_volume']
        total_liquidity = metrics['total_liquidity']
        total_buys = metrics['total_buys']
        total_sells = metrics['total_sells']
        buy_sell_ratio = total_buys / total_sells if total_sells > 0 else 0

        # 价格一致性分析
        min_price = metrics['price_range']['min'] if metrics['price_range']['min'] != float('inf') else 0
        max_price = metrics['price_range']['max']
        if min_price > 0 and max_price > 0:
            price_diff = ((max_price - min_price) / min_price * 100)
        else:
            price_diff = 0

        # 技术指标
        avg_price_change = sum([data['price_change_24h'] for data in analysis.values()]) / len(analysis) if analysis else 0
        volume_to_mcap = (total_volume / total_market_cap * 100) if total_market_cap > 0 else 0

        # 计算增强指标
        # 1. 交易活跃度 (平均每个交易对的交易笔数)
        total_transactions = total_buys + total_sells
        avg_transactions_per_pair = total_transactions / len(pairs_data) if len(pairs_data) > 0 else 0

        # 2. 市场深度 (流动性与交易量的比值)
        market_depth = (total_liquidity / total_volume) if total_volume > 0 else 0

        # 3. 情绪指数 (基于价格变化和买卖比的综合评分)
        sentiment_score = sentiment.get('score', 50)

        # 4. 价格一致性 (价格差异百分比)
        price_consistency = price_diff

        # 生成积极因素HTML
        positive_factors = []
        if buy_sell_ratio > 1.2:
            positive_factors.append(f"强劲的买入压力: 买卖比例{buy_sell_ratio:.2f}:1")
        if total_volume > 1000000:
            positive_factors.append(f"高交易活跃度: 日交易量${total_volume/1000000:.0f}M")
        if len(metrics['dex_distribution']) >= 3:
            positive_factors.append(f"多DEX支持: 分布在{len(metrics['dex_distribution'])}个DEX")

        factors_html = "".join([f"<li>{factor}</li>" for factor in positive_factors])

        # 生成交易对表格HTML - 显示前10个交易对
        pairs_table_html = ""
        if analysis:
            sorted_pairs = sorted(analysis.items(), key=lambda x: x[1].get('volume_24h', 0), reverse=True)
            for pair_addr, data in sorted_pairs[:10]:  # 只显示前10个
                change_color = "#28A745" if data['price_change_24h'] > 0 else "#DC3545"

                # 格式化交易量显示
                volume = data['volume_24h']
                if volume >= 1000000:
                    volume_str = f"${volume/1000000:.2f}M"
                elif volume >= 1000:
                    volume_str = f"${volume/1000:.1f}K"
                else:
                    volume_str = f"${volume:.0f}"

                # 格式化价格显示
                price = data['price_usd']
                if price >= 1:
                    price_str = f"${price:.4f}"
                elif price >= 0.01:
                    price_str = f"${price:.5f}"
                else:
                    price_str = f"${price:.6f}"

                pairs_table_html += f"""
                <tr>
                    <td class="pair-cell">
                        <span class="pair-name">{data['base_token']}/{data['quote_token']}</span>
                        <span class="pair-dex">{data['dex']}</span>
                    </td>
                    <td class="number-cell">{price_str}</td>
                    <td class="percent-cell" style="color: {change_color};">{data['price_change_24h']:+.2f}%</td>
                    <td class="number-cell">{volume_str}</td>
                </tr>"""

        # 生成流动性分布HTML - 显示前5个，优化显示格式
        liquidity_html = ""
        if analysis:
            sorted_by_liquidity = sorted(analysis.items(), key=lambda x: x[1].get('liquidity_usd', 0), reverse=True)
            for i, (pair_addr, data) in enumerate(sorted_by_liquidity[:5], 1):
                if data['liquidity_usd'] > 10:
                    percentage = (data['liquidity_usd'] / total_liquidity * 100) if total_liquidity > 0 else 0

                    # 格式化流动性显示
                    liquidity = data['liquidity_usd']
                    if liquidity >= 1000000:
                        liquidity_str = f"${liquidity/1000000:.2f}M"
                    elif liquidity >= 1000:
                        liquidity_str = f"${liquidity/1000:.1f}K"
                    else:
                        liquidity_str = f"${liquidity:.0f}"

                    liquidity_html += f"""
                    <tr>
                        <td class="pair-cell">
                            <span class="pair-name">{data['base_token']}/{data['quote_token']}</span>
                            <span class="pair-dex">{data['dex']}</span>
                        </td>
                        <td class="number-cell">{liquidity_str}</td>
                        <td class="percent-cell">{percentage:.1f}%</td>
                    </tr>"""

        # 使用专业的HTML模板 (基于201946.html的完整格式)
        report = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告 (最终复刻版)</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {{
            --bg-primary: #0D1117;
            --bg-secondary: #101419;
            --bg-module: #161B22;
            --text-primary: #E6EDF3;
            --text-secondary: #B0BAC6;
            --text-muted: #7D8590;
            --accent-primary: #F2BC8C;
            --border-primary: #30363D;
            --border-secondary: #21262D;
            --success: #28A745;
            --danger: #F59E0B;

            /* 字体大小变量 - 弹性计算 */
            --base-font-size: 1rem;
            --small-font-size: 0.85rem;
            --medium-font-size: 1.1rem;
            --large-font-size: 1.3rem;
            --xlarge-font-size: 1.6rem;
            --xxlarge-font-size: 2rem;

            /* 间距变量 */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 0.75rem;
            --spacing-lg: 1rem;
            --spacing-xl: 1.5rem;
        }}
        *, *::before, *::after {{
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }}
        body, html {{
            font-family: 'Noto Sans SC', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: var(--base-font-size);
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            overflow: hidden;
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
        }}
        .report-container {{
            width: 1280px;
            height: 720px;
            background: var(--bg-primary);
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            position: relative;
        }}
        .report-header {{
            padding: 8px 20px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-primary);
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
        }}
        .header-title h1 {{
            font-family: 'Noto Sans SC', sans-serif;
            font-size: var(--xlarge-font-size);
            font-weight: 700;
        }}
        .header-title h1 .logo {{
            font-family: 'JetBrains Mono', monospace;
            margin-right: 6px;
            color: var(--accent-primary);
        }}
        .header-meta {{
            background-color: #0D1117;
            border-radius: 12px;
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--small-font-size);
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
        }}
        .header-meta strong {{
            color: var(--text-secondary);
        }}
        .report-main {{
            flex-grow: 1;
            padding: 10px;
            display: flex;
            gap: 10px;
            overflow: hidden;
            height: calc(720px - 50px - 25px);
        }}
        .left-column {{
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }}
        .center-column {{
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }}
        .right-column {{
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }}
        .module-card {{
            background: var(--bg-module);
            border-radius: 6px;
            border: 1px solid var(--border-primary);
            padding: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }}
        .module-card .module-title {{
            font-size: var(--medium-font-size);
            font-weight: 500;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            flex-shrink: 0;
        }}
        .module-card.stretchy {{
            flex-grow: 1;
            min-height: 0;
        }}
        .analysis-list {{
            list-style: none;
            font-size: var(--base-font-size);
            padding-left: var(--spacing-xs);
            overflow: hidden;
            flex-grow: 1;
        }}
        .analysis-list li {{
            margin-bottom: var(--spacing-sm);
            line-height: 1.3;
        }}
        .enhanced-metrics-table {{
            width: 100%;
            border-collapse: collapse;
            flex-grow: 1;
        }}
        .enhanced-metrics-table td {{
            padding: var(--spacing-sm) var(--spacing-xs);
            border-bottom: 1px solid var(--border-secondary);
            font-size: var(--base-font-size);
            vertical-align: middle;
        }}
        .enhanced-metrics-table tr:last-child td {{
            border-bottom: none;
            padding-bottom: 0;
        }}
        .enhanced-metrics-table tr:first-child td {{
            padding-top: 0;
        }}
        .enhanced-metrics-table .metric-label {{
            color: var(--text-muted);
            line-height: 1.2;
        }}
        .enhanced-metrics-table .metric-value {{
            font-weight: 500;
            text-align: right;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
        }}
        .core-metrics-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-bottom: 0;
        }}
        .metric-item {{
            background-color: var(--bg-secondary);
            border-radius: 4px;
            padding: var(--spacing-md);
            text-align: center;
            border: 1px solid var(--border-secondary);
        }}
        .metric-item .label {{
            font-size: var(--small-font-size);
            color: var(--text-muted);
            margin-bottom: var(--spacing-xs);
            display: block;
        }}
        .metric-item .value {{
            font-size: var(--large-font-size);
            font-weight: 700;
            font-family: 'JetBrains Mono', monospace;
        }}
        .chart-module {{
            flex-grow: 1;
            padding: 12px;
            min-height: 0;
        }}
        .chart-container {{
            flex-grow: 1;
            min-height: 0;
            height: 100%;
        }}
        #chart_container {{
            width: 100%;
            height: 100%;
        }}
        .table-module {{
            flex-grow: 1;
            min-height: 0;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
        }}
        .table-container {{
            flex-grow: 1;
            max-height: 100%;
            min-height: 0;
            display: flex;
            flex-direction: column;
        }}
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            font-size: var(--small-font-size);
            flex-grow: 1;
            height: 100%;
            table-layout: fixed;
        }}
        .data-table th {{
            background-color: var(--bg-secondary);
            color: var(--text-muted);
            font-weight: 500;
            padding: var(--spacing-sm);
            text-align: center;
            border-bottom: 1px solid var(--border-primary);
            position: sticky;
            top: 0;
            z-index: 1;
            white-space: nowrap;
        }}
        .data-table td {{
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-secondary);
            vertical-align: middle;
            text-align: center;
            word-wrap: break-word;
        }}
        .data-table tr:hover {{
            background-color: var(--bg-secondary);
        }}
        .data-table tbody {{
            height: 100%;
        }}

        /* 主要交易对表格列宽 - 调整宽度避免截断 */
        .data-table.pairs-table {{
            table-layout: fixed;
        }}
        .data-table.pairs-table th:nth-child(1),
        .data-table.pairs-table td:nth-child(1) {{
            width: 35%;
            text-align: left;
        }}
        .data-table.pairs-table th:nth-child(2),
        .data-table.pairs-table td:nth-child(2) {{
            width: 25%;
            text-align: right;
        }}
        .data-table.pairs-table th:nth-child(3),
        .data-table.pairs-table td:nth-child(3) {{
            width: 20%;
            text-align: right;
        }}
        .data-table.pairs-table th:nth-child(4),
        .data-table.pairs-table td:nth-child(4) {{
            width: 20%;
            text-align: right;
        }}

        /* 流动性分布表格列宽 - 流动性和占比往左移 */
        .data-table.liquidity-table {{
            table-layout: fixed;
        }}
        .data-table.liquidity-table th:nth-child(1),
        .data-table.liquidity-table td:nth-child(1) {{
            width: 50%;
            text-align: left;
        }}
        .data-table.liquidity-table th:nth-child(2),
        .data-table.liquidity-table td:nth-child(2) {{
            width: 30%;
            text-align: right !important;
        }}
        .data-table.liquidity-table th:nth-child(3),
        .data-table.liquidity-table td:nth-child(3) {{
            width: 20%;
            text-align: right !important;
        }}

        /* 确保流动性表格数据与表头对齐 */
        .data-table.liquidity-table .number-cell {{
            text-align: right !important;
        }}
        .data-table.liquidity-table .percent-cell {{
            text-align: right !important;
        }}

        /* 交易对列特殊样式 */
        .data-table .pair-cell {{
            white-space: normal;
            line-height: 1.2;
            font-size: var(--small-font-size);
            text-align: left !important;
            word-wrap: break-word;
            overflow: visible;
        }}
        .data-table .pair-name {{
            font-weight: 600;
            color: var(--text-primary);
            display: block;
        }}
        .data-table .pair-dex {{
            color: var(--text-muted);
            font-size: calc(var(--small-font-size) - 1px);
            display: block;
            margin-top: 2px;
        }}
        /* 数值列样式 */
        .data-table .number-cell {{
            text-align: right !important;
            font-family: 'JetBrains Mono', monospace;
            font-weight: 500;
            white-space: nowrap;
            overflow: visible;
        }}
        /* 百分比列样式 */
        .data-table .percent-cell {{
            text-align: right !important;
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
            white-space: nowrap;
            overflow: visible;
        }}
        /* 响应式调整 */
        @media (max-width: 1200px) {{
            .data-table {{
                font-size: calc(var(--small-font-size) - 1px);
            }}
            .data-table th, .data-table td {{
                padding: calc(var(--spacing-sm) - 2px);
            }}
        }}
        .footer {{
            padding: 8px 20px;
            background: var(--bg-secondary);
            border-top: 1px solid var(--border-primary);
            text-align: center;
            font-size: var(--small-font-size);
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
            flex-shrink: 0;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <div class="header-title">
                <h1><span class="logo">🐭 PYTHIA</span>分析报告</h1>
            </div>
            <div class="header-meta">
                <span><strong>Token:</strong> {self.pythia_token_address[:12]}...</span> |
                <span><strong>Chain:</strong> Solana</span> |
                <span><strong>Time:</strong> {now.strftime('%Y-%m-%d %H:%M')}</span>
            </div>
        </header>
        <main class="report-main">
            <!-- LEFT COLUMN -->
            <div class="left-column">
                <div class="module-card">
                    <h2 class="module-title">✅ 积极因素</h2>
                    <ul class="analysis-list">{factors_html}</ul>
                </div>
                <div class="module-card stretchy">
                    <h2 class="module-title">🔬 增强指标</h2>
                    <table class="enhanced-metrics-table">
                        <tbody>
                            <tr>
                                <td class="metric-label">平均价格变化</td>
                                <td class="metric-value">{avg_price_change:.2f}%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">价格一致性</td>
                                <td class="metric-value">{price_consistency:.2f}%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">交易活跃度</td>
                                <td class="metric-value">{avg_transactions_per_pair:.0f} 笔/对</td>
                            </tr>
                            <tr>
                                <td class="metric-label">市场深度</td>
                                <td class="metric-value">{market_depth:.3f}</td>
                            </tr>
                            <tr>
                                <td class="metric-label">情绪指数</td>
                                <td class="metric-value">{sentiment['sentiment']}</td>
                            </tr>
                            <tr>
                                <td class="metric-label">买卖比</td>
                                <td class="metric-value">{buy_sell_ratio:.2f}:1</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- CENTER COLUMN -->
            <div class="center-column">
                <div class="module-card">
                    <div class="core-metrics-grid">
                        <div class="metric-item">
                            <div class="label">市值</div>
                            <div class="value">${total_market_cap/1000000:.1f}M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">24h交易量</div>
                            <div class="value">${total_volume/1000000:.1f}M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">流动性</div>
                            <div class="value">${total_liquidity/1000000:.1f}M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">换手率</div>
                            <div class="value">{volume_to_mcap:.1f}%</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">交易对数</div>
                            <div class="value">{len(pairs_data)}</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">总交易笔数</div>
                            <div class="value">{total_buys + total_sells:,}</div>
                        </div>
                    </div>
                </div>
                <div class="module-card stretchy chart-module">
                    <h2 class="module-title">📈 价格走势图 (24H)</h2>
                    <div class="chart-container">
                        <div id="chart_container"></div>
                    </div>
                </div>
            </div>
            <!-- RIGHT COLUMN -->
            <div class="right-column">
                <div class="module-card table-module">
                    <h2 class="module-title">💰 主要交易对</h2>
                    <div class="table-container">
                        <table class="data-table pairs-table">
                            <thead>
                                <tr><th>交易对</th><th>价格</th><th>24h%</th><th>交易量</th></tr>
                            </thead>
                            <tbody>{pairs_table_html}</tbody>
                        </table>
                    </div>
                </div>
                <div class="module-card table-module">
                    <h2 class="module-title">💧 流动性分布</h2>
                    <div class="table-container">
                        <table class="data-table liquidity-table">
                            <thead>
                                <tr><th>交易对</th><th>流动性</th><th>占比</th></tr>
                            </thead>
                            <tbody>{liquidity_html}</tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
        <footer class="footer">
            PYTHIA AI 增强分析工具 v3.0 | "数据驱动决策，理性投资未来"
        </footer>
    </div>
    <!-- TradingView Widget Script - 专业版本 -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        // 确保TradingView全局变量可用，兼容html_to_image_headless.py
        window.TradingView = window.TradingView || {{}};

        // 图表加载状态标记
        window.chartLoaded = false;
        window.tradingViewLoaded = false;

        function initializeTradingViewChart() {{
            try {{
                // 检查容器是否存在
                const container = document.getElementById('chart_container');
                if (!container) {{
                    console.error('Chart container not found');
                    return;
                }}

                // 标记TradingView已加载
                window.tradingViewLoaded = true;

                // 创建TradingView图表 - 使用专业配置
                const widget = new TradingView.widget({{
                    "autosize": true,
                    "symbol": "CRYPTO:PYTHIAUSD",
                    "interval": "30",
                    "theme": "dark",
                    "style": "1",
                    "locale": "zh_CN",
                    "toolbar_bg": "#161B22",
                    "enable_publishing": false,
                    "hide_top_toolbar": true,
                    "hide_side_toolbar": true,
                    "hide_legend": true,
                    "container_id": "chart_container",
                    "overrides": {{
                        "paneProperties.background": "#161B22",
                        "paneProperties.vertGridProperties.color": "rgba(255,255,255,0.05)",
                        "paneProperties.horzGridProperties.color": "rgba(255,255,255,0.05)",
                        "mainSeriesProperties.candleStyle.upColor": "#28A745",
                        "mainSeriesProperties.candleStyle.downColor": "#DC3545",
                        "mainSeriesProperties.candleStyle.borderUpColor": "#28A745",
                        "mainSeriesProperties.candleStyle.borderDownColor": "#DC3545",
                        "scalesProperties.textColor": "#7D8590"
                    }},
                    "onChartReady": function() {{
                        // 图表准备就绪
                        window.chartLoaded = true;
                        console.log('TradingView chart loaded successfully');
                    }}
                }});

                // 备用标记，防止onChartReady不触发
                setTimeout(function() {{
                    window.chartLoaded = true;
                }}, 5000);

            }} catch (error) {{
                console.error('TradingView chart initialization error:', error);
                // 即使出错也标记为已加载，避免无限等待
                window.chartLoaded = true;
                window.tradingViewLoaded = true;
            }}
        }}

        // 页面加载完成后初始化图表
        if (document.readyState === 'loading') {{
            document.addEventListener('DOMContentLoaded', initializeTradingViewChart);
        }} else {{
            initializeTradingViewChart();
        }}

        // 窗口加载完成后的备用初始化
        window.addEventListener('load', function() {{
            setTimeout(function() {{
                if (!window.tradingViewLoaded) {{
                    initializeTradingViewChart();
                }}
                // 最终备用标记
                setTimeout(function() {{
                    window.chartLoaded = true;
                    window.tradingViewLoaded = true;
                }}, 3000);
            }}, 1000);
        }});
    </script>
</body>
</html>"""

        return report

    def auto_generate_report(self, quiet_mode: bool = False, auto_send: bool = False) -> str:
        """自动生成报告并保存"""
        try:
            if not quiet_mode:
                print("🐭 开始自动生成PYTHIA分析报告...")

            # 生成报告
            report_content = self.generate_professional_report()

            if not report_content or "网络连接异常" in report_content:
                if not quiet_mode:
                    print("❌ 报告生成失败")
                return ""

            # 保存报告
            filepath = self.save_report_to_file(report_content)

            if not filepath:
                if not quiet_mode:
                    print("❌ 报告保存失败")
                return ""

            return filepath

        except Exception as e:
            if not quiet_mode:
                print(f"❌ 自动生成报告出错: {e}")
            return ""

    def start_auto_scheduler(self):
        """启动自动调度器 - 根据配置文件设置"""
        auto_run_config = ANALYSIS_CONFIG.get("auto_run_settings", {})
        interval_minutes = auto_run_config.get("interval_minutes", 60)
        run_on_startup = auto_run_config.get("run_on_startup", True)

        print("🐭 PYTHIA自动报告发送器")
        print("=" * 50)
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 发送间隔: {interval_minutes}分钟")
        print(f"📱 启动时发送: {'是' if run_on_startup else '否'}")
        print()

        # 设置定时任务
        schedule.every(interval_minutes).minutes.do(self.generate_and_send_report)

        # 显示下次执行时间
        next_run = schedule.next_run()
        print(f"⏰ 下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # 启动时立即执行一次
        if run_on_startup:
            print("🚀 启动时立即执行一次...")
            self.generate_and_send_report()
            print()

        print("🔄 进入定时运行模式...")
        print("💡 按 Ctrl+C 停止运行")
        print("=" * 50)

        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次

        except KeyboardInterrupt:
            print(f"\n\n⏹️ 用户停止运行")
            print("👋 感谢使用PYTHIA自动报告发送器！")

    def start_continuous_mode(self):
        """启动持续运行模式（备用方案，不使用schedule库）"""
        auto_run_config = ANALYSIS_CONFIG.get("auto_run_settings", {})
        interval_minutes = auto_run_config.get("interval_minutes", 60)
        run_on_startup = auto_run_config.get("run_on_startup", True)

        print("🐭 PYTHIA持续运行模式")
        print("=" * 50)
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 发送间隔: {interval_minutes}分钟")
        print(f"📱 启动时发送: {'是' if run_on_startup else '否'}")
        print()

        # 启动时立即执行一次
        if run_on_startup:
            print("🚀 启动时立即执行一次...")
            self.generate_and_send_report()
            print()

        print("🔄 进入持续运行模式...")
        print("💡 按 Ctrl+C 停止运行")
        print("=" * 50)

        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - 等待{interval_minutes}分钟后执行...")
                time.sleep(interval_minutes * 60)  # 转换为秒

                print(f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始生成报告")
                self.generate_and_send_report()

        except KeyboardInterrupt:
            print(f"\n\n⏹️ 用户停止运行")
            print("👋 感谢使用PYTHIA持续报告生成器！")

    def generate_and_send_report(self):
        """生成并存储报告 - 只存储不发送到Telegram"""
        try:
            print(f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始生成报告")
            print("=" * 60)

            print("📊 正在生成PYTHIA分析报告...")
            # 只生成报告，不自动发送
            filepath = self.auto_generate_report(quiet_mode=True, auto_send=False)

            if not filepath:
                print("❌ 报告生成失败")
                return False

            print(f"✅ 报告生成成功: {os.path.basename(filepath)}")
            print("💾 报告已保存到本地，跳过Telegram发送")
            print("✅ 报告存储成功")
            print("=" * 60)
            return True

        except Exception as e:
            print(f"❌ 生成报告出错: {e}")
            return False


def main():
    """主函数 - 默认启动自动推送模式"""
    analyzer = PythiaIntegratedAnalyzer()

    print("🐭 PYTHIA代币全面分析与报告发送器 - 完整整合版本")
    print("=" * 60)

    # 从配置文件读取自动运行设置
    auto_run_config = ANALYSIS_CONFIG.get("auto_run_settings", {})

    if auto_run_config.get("enabled", True):
        # 默认启动自动推送模式
        print("🚀 自动启动定时推送模式")
        print(f"⏰ 推送间隔: {auto_run_config.get('interval_minutes', 60)}分钟")
        print(f"📱 启动时发送: {'是' if auto_run_config.get('run_on_startup', True) else '否'}")
        print()

        # 检查schedule库
        try:
            import schedule
            analyzer.start_auto_scheduler()
        except ImportError:
            print("❌ 未安装schedule库")
            print("💡 请安装: pip install schedule")
            print("🔄 切换到持续运行模式...")
            analyzer.start_continuous_mode()
    else:
        # 如果配置中禁用了自动运行，显示菜单
        print("⚠️ 自动运行已在配置中禁用，显示手动选择菜单:")
        print("1. 生成完整报告")
        print("2. 生成报告并存储")
        print("3. 启动HTML监控模式")
        print()

        try:
            choice = input("请输入选择 (1-3): ").strip()

            if choice == "1":
                print("\n🚀 生成完整报告...")
                filepath = analyzer.auto_generate_report()
                if filepath:
                    print(f"✅ 报告已生成: {filepath}")

            elif choice == "2":
                print("\n🚀 生成报告并存储...")
                filepath = analyzer.auto_generate_report(auto_send=True)
                if filepath:
                    print(f"✅ 报告已生成并存储: {filepath}")

            elif choice == "3":
                html_monitor_main()

            else:
                print("❌ 无效选择")

        except KeyboardInterrupt:
            print("\n👋 已取消")

    def start_auto_scheduler(self):
        """启动自动调度器 - 根据配置文件设置"""
        auto_run_config = ANALYSIS_CONFIG.get("auto_run_settings", {})
        interval_minutes = auto_run_config.get("interval_minutes", 60)
        run_on_startup = auto_run_config.get("run_on_startup", True)

        print("🐭 PYTHIA自动报告发送器")
        print("=" * 50)
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 发送间隔: {interval_minutes}分钟")
        print(f"📱 启动时发送: {'是' if run_on_startup else '否'}")
        print()

        # 设置定时任务
        schedule.every(interval_minutes).minutes.do(self.generate_and_send_report)

        # 显示下次执行时间
        next_run = schedule.next_run()
        print(f"⏰ 下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # 启动时立即执行一次
        if run_on_startup:
            print("🚀 启动时立即执行一次...")
            self.generate_and_send_report()
            print()

        print("🔄 进入定时运行模式...")
        print("💡 按 Ctrl+C 停止运行")
        print("=" * 50)

        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次

        except KeyboardInterrupt:
            print(f"\n\n⏹️ 用户停止运行")
            print("👋 感谢使用PYTHIA自动报告发送器！")

    def start_continuous_mode(self):
        """启动持续运行模式（备用方案，不使用schedule库）"""
        auto_run_config = ANALYSIS_CONFIG.get("auto_run_settings", {})
        interval_minutes = auto_run_config.get("interval_minutes", 60)
        run_on_startup = auto_run_config.get("run_on_startup", True)

        print("🐭 PYTHIA持续运行模式")
        print("=" * 50)
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 发送间隔: {interval_minutes}分钟")
        print(f"📱 启动时发送: {'是' if run_on_startup else '否'}")
        print()

        # 启动时立即执行一次
        if run_on_startup:
            print("🚀 启动时立即执行一次...")
            self.generate_and_send_report()
            print()

        print("🔄 进入持续运行模式...")
        print("💡 按 Ctrl+C 停止运行")
        print("=" * 50)

        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - 等待{interval_minutes}分钟后执行...")
                time.sleep(interval_minutes * 60)  # 转换为秒

                print(f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始生成报告")
                self.generate_and_send_report()

        except KeyboardInterrupt:
            print(f"\n\n⏹️ 用户停止运行")
            print("👋 感谢使用PYTHIA持续报告生成器！")


def html_monitor_main():
    """HTML文件监控主函数 - 持续监控模式"""
    analyzer = PythiaIntegratedAnalyzer()

    print("🐭 PYTHIA HTML转图片监控器 - 持续监控模式")
    print("=" * 50)
    print("🔄 程序将持续运行，监控新的HTML文件...")
    print("📸 自动转换为图片并发送到Telegram群组")
    print("💡 按 Ctrl+C 退出程序")
    print("=" * 50)

    processed_files = set()  # 记录已处理的文件

    try:
        while True:
            try:
                # 查找最新HTML文件
                latest_file = analyzer.html_converter.find_latest_html()

                if latest_file and str(latest_file) not in processed_files:
                    # 检查冷却时间
                    if analyzer.html_converter.check_cooldown():
                        print(f"⏰ 跳过处理 {latest_file.name} - 仍在冷却期")
                    else:
                        print(f"\n🆕 发现新文件: {latest_file.name}")
                        print("-" * 30)

                        try:
                            # 转换HTML为图片
                            result = analyzer.html_converter.convert_html_to_image(str(latest_file))
                            if result:
                                print(f"\n🎉 转换完成! 图片: {result}")
                                processed_files.add(str(latest_file))

                                # 发送到Telegram群组
                                if ENABLE_TELEGRAM_SEND and analyzer.telegram_bot_token:
                                    target_chat_ids = analyzer.get_target_chat_ids()

                                    if target_chat_ids:
                                        success_count = 0
                                        for chat_id in target_chat_ids:
                                            success = analyzer.html_converter.send_to_telegram(
                                                result, latest_file.name,
                                                analyzer.telegram_bot_token, chat_id
                                            )
                                            if success:
                                                success_count += 1
                                                print(f"✅ 图片已成功发送到群组: {chat_id}")
                                            else:
                                                print(f"❌ 发送到群组 {chat_id} 失败")

                                        if success_count > 0:
                                            print(f"🎉 图片已成功发送到 {success_count}/{len(target_chat_ids)} 个群组")
                                        else:
                                            print("❌ 所有群组发送失败")
                                    else:
                                        print("⚠️ 未找到目标群组，跳过发送")
                                else:
                                    if not ENABLE_TELEGRAM_SEND:
                                        print("🔇 Telegram发送已关闭")
                                    else:
                                        print("⚠️ Telegram Bot Token未设置，跳过发送")

                                # 记录处理时间并开始冷却
                                analyzer.html_converter.last_process_time = datetime.now()
                                print(f"\n❄️ 开始30分钟冷却期...")
                                print(f"🕐 下次可处理时间: {(analyzer.html_converter.last_process_time + timedelta(minutes=30)).strftime('%H:%M:%S')}")

                            else:
                                print(f"\n❌ 转换失败")
                                processed_files.add(str(latest_file))
                        except Exception as e:
                            print(f"\n❌ 转换错误: {e}")
                            # 即使失败也标记为已处理，避免重复尝试
                            processed_files.add(str(latest_file))

                # 等待10秒后再次检查
                print(f"\n⏰ 等待新文件... (已处理 {len(processed_files)} 个文件)")
                time.sleep(10)

            except KeyboardInterrupt:
                print("\n\n👋 用户中断，程序退出")
                break
            except Exception as e:
                print(f"\n⚠️ 监控异常: {e}")
                print("🔄 5秒后继续监控...")
                time.sleep(5)

    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--monitor":
            # 启动HTML监控模式
            html_monitor_main()
        elif sys.argv[1] == "--reset-size":
            # 重置浏览器尺寸配置
            print("🔄 重置浏览器尺寸配置...")
            analyzer = PythiaIntegratedAnalyzer()
            analyzer.html_converter.reset_optimal_size()
            print("✅ 尺寸配置已重置，下次运行将重新检测最佳尺寸")
        elif sys.argv[1] == "--help":
            # 显示帮助信息
            print("🐭 PYTHIA代币分析器 - 使用说明")
            print("=" * 50)
            print("python pythia_integrated_complete.py           # 启动正常分析模式")
            print("python pythia_integrated_complete.py --monitor # 启动HTML监控模式")
            print("python pythia_integrated_complete.py --reset-size # 重置浏览器尺寸配置")
            print("python pythia_integrated_complete.py --help    # 显示此帮助信息")
        else:
            print(f"❌ 未知参数: {sys.argv[1]}")
            print("💡 使用 --help 查看可用参数")
    else:
        # 启动正常的分析模式
        main()
