#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试知识库集成功能
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bot import KnowledgeManager

def test_knowledge_manager():
    """测试知识库管理器"""
    print("🧪 测试知识库管理器...")
    
    # 创建知识库管理器实例
    km = KnowledgeManager()
    
    # 测试加载知识库
    print("\n📚 测试加载知识库...")
    knowledge = km.get_knowledge_base()
    
    if knowledge:
        print(f"✅ 知识库加载成功，包含 {len(knowledge)} 个主要部分")
        print(f"📋 主要部分: {list(knowledge.keys())}")
    else:
        print("❌ 知识库加载失败")
        return False
    
    # 测试搜索功能
    print("\n🔍 测试知识库搜索功能...")
    
    test_queries = [
        "PYTHIA项目是什么",
        "代币价格",
        "团队成员",
        "技术原理",
        "大鼠实验"
    ]
    
    for query in test_queries:
        print(f"\n🔎 搜索: '{query}'")
        results = km.search_knowledge(query)
        
        if results:
            print(f"✅ 找到 {len(results)} 个相关结果:")
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result['title']} (相关性: {result['relevance']})")
        else:
            print("❌ 未找到相关结果")
    
    # 测试上下文获取
    print("\n📝 测试上下文获取功能...")
    
    context_queries = [
        "告诉我关于PYTHIA项目的信息",
        "PYTHIA代币的价格如何",
        "团队有哪些成员"
    ]
    
    for query in context_queries:
        print(f"\n📋 获取上下文: '{query}'")
        context = km.get_contextual_knowledge(query)
        
        if context:
            print(f"✅ 获取到上下文 ({len(context)} 字符):")
            print(f"📄 预览: {context[:200]}...")
        else:
            print("❌ 未获取到相关上下文")
    
    return True

def test_knowledge_base_structure():
    """测试知识库结构"""
    print("\n🏗️ 测试知识库结构...")
    
    try:
        with open('pythia_knowledge_base.json', 'r', encoding='utf-8') as f:
            knowledge = json.load(f)
        
        # 检查必要的部分
        required_sections = [
            'metadata',
            'project_overview', 
            'company_info',
            'leadership_team',
            'scientific_core',
            'token_economics'
        ]
        
        print("📊 检查必要部分:")
        for section in required_sections:
            if section in knowledge:
                print(f"  ✅ {section}")
            else:
                print(f"  ❌ {section} (缺失)")
        
        # 显示统计信息
        print(f"\n📈 知识库统计:")
        print(f"  📁 总部分数: {len(knowledge)}")
        print(f"  📄 总字符数: {len(json.dumps(knowledge, ensure_ascii=False))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 知识库结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试PYTHIA知识库集成...")
    
    # 测试知识库结构
    if not test_knowledge_base_structure():
        print("❌ 知识库结构测试失败，退出")
        return
    
    # 测试知识库管理器
    if not test_knowledge_manager():
        print("❌ 知识库管理器测试失败，退出")
        return
    
    print("\n🎉 所有测试通过！知识库集成功能正常工作。")
    print("\n💡 建议:")
    print("  1. 知识库已成功集成到bot.py中")
    print("  2. 机器人现在可以根据用户问题智能检索相关知识")
    print("  3. 知识库内容会自动添加到系统提示词中")
    print("  4. 缓存机制确保性能优化")

if __name__ == "__main__":
    main()
