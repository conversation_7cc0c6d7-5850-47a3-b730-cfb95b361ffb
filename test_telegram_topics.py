#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram话题发送功能测试脚本
"""

import requests
import json
import os
from datetime import datetime

# 导入配置
try:
    from config import OUTPUT_CONFIG
except ImportError:
    print("❌ 无法导入config.py，请确保配置文件存在")
    exit(1)

class TelegramTopicTester:
    """Telegram话题发送测试器"""
    
    def __init__(self):
        try:
            self.bot_token = OUTPUT_CONFIG["telegram"]["bot_token"]
            self.topic_configs = OUTPUT_CONFIG["telegram"].get("topic_configs", {})
        except KeyError as e:
            print(f"❌ 配置文件缺少必要项: {e}")
            exit(1)
    
    def send_test_message(self, chat_id, message, thread_id=None):
        """发送测试消息"""
        url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
        
        data = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        if thread_id:
            data['message_thread_id'] = thread_id
        
        try:
            response = requests.post(url, data=data, timeout=10)
            return response.status_code == 200, response.json()
        except Exception as e:
            return False, str(e)
    
    def test_topic_configuration(self):
        """测试话题配置"""
        print("🧪 测试Telegram话题配置")
        print("=" * 50)
        
        if not self.topic_configs:
            print("❌ 未找到话题配置")
            return False
        
        print(f"📋 找到 {len(self.topic_configs)} 个话题配置:")
        
        for topic_name, config in self.topic_configs.items():
            print(f"\n📌 话题: {topic_name}")
            print(f"   描述: {config.get('description', '无描述')}")
            print(f"   频道ID: {config.get('chat_id', '未配置')}")
            print(f"   话题ID: {config.get('thread_id', '未配置')}")
            
            # 验证配置完整性
            if not config.get('chat_id'):
                print("   ❌ 缺少chat_id配置")
                continue
            
            if not config.get('thread_id'):
                print("   ⚠️ 缺少thread_id配置，将使用普通发送")
            else:
                print("   ✅ 配置完整")
        
        return True
    
    def test_bot_permissions(self):
        """测试机器人权限"""
        print("\n🔐 测试机器人权限")
        print("=" * 50)
        
        # 获取机器人信息
        url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
        
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                bot_info = response.json()['result']
                print(f"✅ 机器人连接成功")
                print(f"   名称: {bot_info.get('first_name', 'Unknown')}")
                print(f"   用户名: @{bot_info.get('username', 'Unknown')}")
                return True
            else:
                print(f"❌ 机器人连接失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 机器人连接异常: {e}")
            return False
    
    def test_topic_sending(self):
        """测试话题发送功能"""
        print("\n📤 测试话题发送功能")
        print("=" * 50)
        
        test_message = f"""🧪 <b>Telegram话题发送测试</b>

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 发送者: PYTHIA分析机器人
📊 功能: 频道话题消息发送

✅ 如果您看到这条消息，说明话题发送功能正常工作！"""

        success_count = 0
        total_count = len(self.topic_configs)
        
        for topic_name, config in self.topic_configs.items():
            print(f"\n📌 测试话题: {topic_name}")
            
            chat_id = config.get('chat_id')
            thread_id = config.get('thread_id')
            
            if not chat_id:
                print("   ❌ 跳过：缺少chat_id")
                continue
            
            # 发送测试消息
            if thread_id:
                print(f"   📤 发送到话题 (Thread ID: {thread_id})...")
                success, result = self.send_test_message(chat_id, test_message, thread_id)
            else:
                print("   📤 发送到频道主区域...")
                success, result = self.send_test_message(chat_id, test_message)
            
            if success:
                print("   ✅ 发送成功")
                success_count += 1
            else:
                print(f"   ❌ 发送失败: {result}")
        
        print(f"\n📊 测试结果: {success_count}/{total_count} 个话题发送成功")
        return success_count > 0
    
    def get_chat_info(self, chat_id):
        """获取聊天信息"""
        url = f"https://api.telegram.org/bot{self.bot_token}/getChat"
        
        try:
            response = requests.post(url, data={'chat_id': chat_id}, timeout=10)
            if response.status_code == 200:
                return True, response.json()['result']
            else:
                return False, response.json()
        except Exception as e:
            return False, str(e)
    
    def analyze_chat_structure(self):
        """分析聊天结构"""
        print("\n🔍 分析聊天结构")
        print("=" * 50)
        
        analyzed_chats = set()
        
        for topic_name, config in self.topic_configs.items():
            chat_id = config.get('chat_id')
            
            if not chat_id or chat_id in analyzed_chats:
                continue
            
            analyzed_chats.add(chat_id)
            
            print(f"\n📱 分析聊天: {chat_id}")
            success, chat_info = self.get_chat_info(chat_id)
            
            if success:
                print(f"   类型: {chat_info.get('type', 'unknown')}")
                print(f"   标题: {chat_info.get('title', 'unknown')}")
                
                # 检查是否支持话题
                if chat_info.get('type') == 'supergroup':
                    if chat_info.get('is_forum', False):
                        print("   ✅ 支持话题功能")
                    else:
                        print("   ⚠️ 不支持话题功能")
                elif chat_info.get('type') == 'channel':
                    print("   📢 频道类型")
                else:
                    print("   ❓ 其他类型")
            else:
                print(f"   ❌ 无法获取信息: {chat_info}")
    
    def run_full_test(self):
        """运行完整测试"""
        print("🚀 开始Telegram话题发送功能测试")
        print("=" * 60)
        
        # 1. 测试配置
        if not self.test_topic_configuration():
            print("❌ 配置测试失败，退出")
            return False
        
        # 2. 测试机器人权限
        if not self.test_bot_permissions():
            print("❌ 机器人权限测试失败，退出")
            return False
        
        # 3. 分析聊天结构
        self.analyze_chat_structure()
        
        # 4. 测试发送功能
        if self.test_topic_sending():
            print("\n🎉 所有测试通过！话题发送功能正常工作。")
            return True
        else:
            print("\n❌ 发送测试失败，请检查配置和权限。")
            return False

def main():
    """主函数"""
    print("🧪 Telegram话题发送功能测试工具")
    print("=" * 60)
    
    # 检查配置文件
    if not os.path.exists('config.py'):
        print("❌ 未找到config.py配置文件")
        return
    
    # 创建测试器
    tester = TelegramTopicTester()
    
    # 运行测试
    success = tester.run_full_test()
    
    if success:
        print("\n💡 使用建议:")
        print("  1. 话题发送功能已正常工作")
        print("  2. 可以在pythia_integrated_complete.py中使用话题发送")
        print("  3. 使用send_to_telegram_topic()方法发送到指定话题")
        print("  4. 使用send_to_multiple_topics()方法发送到多个话题")
    else:
        print("\n🔧 故障排除建议:")
        print("  1. 检查机器人token是否正确")
        print("  2. 确认机器人是频道/群组管理员")
        print("  3. 验证chat_id和thread_id是否正确")
        print("  4. 确认频道已启用话题功能")

if __name__ == "__main__":
    main()
